{"__meta": {"id": "01K53Q6ZSNGGS1DXF27HZWMK98", "datetime": "2025-09-14 08:59:35", "utime": **********.6061, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1757840374.714677, "end": **********.606113, "duration": 0.8914358615875244, "duration_str": "891ms", "measures": [{"label": "Booting", "start": 1757840374.714677, "relative_start": 0, "end": **********.027575, "relative_end": **********.027575, "duration": 0.****************, "duration_str": "313ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.027589, "relative_start": 0.****************, "end": **********.606114, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.038737, "relative_start": 0.*****************, "end": **********.03979, "relative_end": **********.03979, "duration": 0.****************, "duration_str": "1.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.hooks.scoresheet-banner", "start": **********.283761, "relative_start": 0.****************, "end": **********.283761, "relative_end": **********.283761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0e562057e53b176eb249937b70a802f8", "start": **********.291152, "relative_start": 0.****************, "end": **********.291152, "relative_end": **********.291152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.345265, "relative_start": 0.6305878162384033, "end": **********.345265, "relative_end": **********.345265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.355113, "relative_start": 0.6404359340667725, "end": **********.355113, "relative_end": **********.355113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.360191, "relative_start": 0.6455140113830566, "end": **********.360191, "relative_end": **********.360191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.366985, "relative_start": 0.6523079872131348, "end": **********.366985, "relative_end": **********.366985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.374382, "relative_start": 0.6597049236297607, "end": **********.374382, "relative_end": **********.374382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.380467, "relative_start": 0.6657898426055908, "end": **********.380467, "relative_end": **********.380467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.51693, "relative_start": 0.802253007888794, "end": **********.51693, "relative_end": **********.51693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.521668, "relative_start": 0.8069908618927002, "end": **********.521668, "relative_end": **********.521668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3e7af8a9f04a41a7583e36fe677e9b1b", "start": **********.523929, "relative_start": 0.8092520236968994, "end": **********.523929, "relative_end": **********.523929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.528114, "relative_start": 0.8134369850158691, "end": **********.528114, "relative_end": **********.528114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ba9cccfb20917de418a9fa74743df081", "start": **********.533705, "relative_start": 0.8190279006958008, "end": **********.533705, "relative_end": **********.533705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.550575, "relative_start": 0.8358979225158691, "end": **********.550575, "relative_end": **********.550575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.568063, "relative_start": 0.8533859252929688, "end": **********.568063, "relative_end": **********.568063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ba9cccfb20917de418a9fa74743df081", "start": **********.570788, "relative_start": 0.8561108112335205, "end": **********.570788, "relative_end": **********.570788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.582332, "relative_start": 0.8676548004150391, "end": **********.582332, "relative_end": **********.582332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.592658, "relative_start": 0.8779809474945068, "end": **********.592658, "relative_end": **********.592658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.604212, "relative_start": 0.8895349502563477, "end": **********.605333, "relative_end": **********.605333, "duration": 0.0011210441589355469, "duration_str": "1.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7780936, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 18, "nb_templates": 18, "templates": [{"name": "filament.hooks.scoresheet-banner", "param_count": null, "params": [], "start": **********.28372, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.phpfilament.hooks.scoresheet-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fscoresheet-banner.blade.php&line=1", "ajax": false, "filename": "scoresheet-banner.blade.php", "line": "?"}}, {"name": "__components::0e562057e53b176eb249937b70a802f8", "param_count": null, "params": [], "start": **********.291114, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/0e562057e53b176eb249937b70a802f8.blade.php__components::0e562057e53b176eb249937b70a802f8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F0e562057e53b176eb249937b70a802f8.blade.php&line=1", "ajax": false, "filename": "0e562057e53b176eb249937b70a802f8.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.345167, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.355077, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.360154, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.366948, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.374348, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.38042, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.516895, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.521633, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::3e7af8a9f04a41a7583e36fe677e9b1b", "param_count": null, "params": [], "start": **********.523895, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/3e7af8a9f04a41a7583e36fe677e9b1b.blade.php__components::3e7af8a9f04a41a7583e36fe677e9b1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F3e7af8a9f04a41a7583e36fe677e9b1b.blade.php&line=1", "ajax": false, "filename": "3e7af8a9f04a41a7583e36fe677e9b1b.blade.php", "line": "?"}}, {"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.52808, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::ba9cccfb20917de418a9fa74743df081", "param_count": null, "params": [], "start": **********.533669, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/ba9cccfb20917de418a9fa74743df081.blade.php__components::ba9cccfb20917de418a9fa74743df081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fba9cccfb20917de418a9fa74743df081.blade.php&line=1", "ajax": false, "filename": "ba9cccfb20917de418a9fa74743df081.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.550539, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.568027, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::ba9cccfb20917de418a9fa74743df081", "param_count": null, "params": [], "start": **********.570753, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/ba9cccfb20917de418a9fa74743df081.blade.php__components::ba9cccfb20917de418a9fa74743df081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fba9cccfb20917de418a9fa74743df081.blade.php&line=1", "ajax": false, "filename": "ba9cccfb20917de418a9fa74743df081.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.582295, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.592622, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}]}, "queries": {"count": 90, "nb_statements": 87, "nb_visible_statements": 90, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09182999999999998, "accumulated_duration_str": "91.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.045496, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'zuNniNVqlMeCDsAImSjdDg0J7ofFi7U7ZN0Quta9' limit 1", "type": "query", "params": [], "bindings": ["zuNniNVqlMeCDsAImSjdDg0J7ofFi7U7ZN0Quta9"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.0466218, "duration": 0.01183, "duration_str": "11.83ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 12.883}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.069884, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 12.883, "width_percent": 1.873}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 64}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.111685, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:64", "source": {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=64", "ajax": false, "filename": "ScoreResource.php", "line": "64"}, "connection": "racoed", "explain": null, "start_percent": 14.756, "width_percent": 2.516}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.11705, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:67", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=67", "ajax": false, "filename": "ScoreResource.php", "line": "67"}, "connection": "racoed", "explain": null, "start_percent": 17.271, "width_percent": 2.08}, {"sql": "select `id`, `name`, `max_score` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 300}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}], "start": **********.145568, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:300", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 300}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=300", "ajax": false, "filename": "ScoreResource.php", "line": "300"}, "connection": "racoed", "explain": null, "start_percent": 19.351, "width_percent": 2.309}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.187775, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 21.66, "width_percent": 2.668}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.19221, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 24.328, "width_percent": 0.653}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-09-14' and date(`semester_end`) >= '2025-09-14' limit 1", "type": "query", "params": [], "bindings": [3, "2025-09-14", "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.195224, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 24.981, "width_percent": 3.844}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.211097, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 28.825, "width_percent": 3.724}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.217039, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 32.549, "width_percent": 0.544}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.219572, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 33.094, "width_percent": 1.35}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.224226, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 34.444, "width_percent": 4.9}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.235079, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 39.344, "width_percent": 0.544}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.237248, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 39.889, "width_percent": 0.436}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.239243, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 40.325, "width_percent": 0.414}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.2413778, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 40.738, "width_percent": 1.198}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 82}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.245822, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:30", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=30", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "30"}, "connection": "racoed", "explain": null, "start_percent": 41.936, "width_percent": 0}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.2476459, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 41.936, "width_percent": 0.61}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.2499418, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 42.546, "width_percent": 0.446}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.251977, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 42.992, "width_percent": 0.403}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}], "start": **********.254079, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:54", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=54", "ajax": false, "filename": "ManageScores.php", "line": "54"}, "connection": "racoed", "explain": null, "start_percent": 43.395, "width_percent": 1.078}, {"sql": "select `id`, `name`, `max_score` from `assessments` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 67}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}], "start": **********.257343, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:67", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=67", "ajax": false, "filename": "ManageScores.php", "line": "67"}, "connection": "racoed", "explain": null, "start_percent": 44.473, "width_percent": 0.512}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1757840675, 'scoreData_68c683f73f41e', 'a:4:{s:8:\\\"students\\\";O:29:\\\"Illuminate\\Support\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:4:{i:0;a:6:{s:2:\\\"id\\\";i:35;s:4:\\\"name\\\";s:21:\\\"AJAYI Olajumoke Janet\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0109\\\";s:16:\\\"assessmentScores\\\";N;s:10:\\\"totalScore\\\";N;s:5:\\\"grade\\\";N;}i:1;a:6:{s:2:\\\"id\\\";i:36;s:4:\\\"name\\\";s:19:\\\"ADEYI TOYIN Rukayat\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0110\\\";s:16:\\\"assessmentScores\\\";N;s:10:\\\"totalScore\\\";N;s:5:\\\"grade\\\";N;}i:2;a:6:{s:2:\\\"id\\\";i:37;s:4:\\\"name\\\";s:23:\\\"KOLAWOLE Favour Deborah\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0113\\\";s:16:\\\"assessmentScores\\\";N;s:10:\\\"totalScore\\\";N;s:5:\\\"grade\\\";N;}i:3;a:6:{s:2:\\\"id\\\";i:38;s:4:\\\"name\\\";s:21:\\\"HAMMED Kazeem Olaitan\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0117\\\";s:16:\\\"assessmentScores\\\";N;s:10:\\\"totalScore\\\";N;s:5:\\\"grade\\\";N;}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}s:12:\\\"tableFilters\\\";a:5:{s:17:\\\"school_session_id\\\";s:1:\\\"3\\\";s:11:\\\"semester_id\\\";s:1:\\\"1\\\";s:8:\\\"level_id\\\";s:1:\\\"1\\\";s:13:\\\"department_id\\\";s:2:\\\"16\\\";s:9:\\\"course_id\\\";s:3:\\\"326\\\";}s:11:\\\"assessments\\\";O:39:\\\"Illuminate\\Database\\Eloquent\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:2:{i:0;O:21:\\\"App\\Models\\Assessment\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:11:\\\"assessments\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:1;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:3:{s:2:\\\"id\\\";i:1;s:4:\\\"name\\\";s:4:\\\"C.A.\\\";s:9:\\\"max_score\\\";i:40;}s:11:\\\"?*?original\\\";a:3:{s:2:\\\"id\\\";i:1;s:4:\\\"name\\\";s:4:\\\"C.A.\\\";s:9:\\\"max_score\\\";i:40;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:0:{}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:1;O:21:\\\"App\\Models\\Assessment\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:11:\\\"assessments\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:1;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:3:{s:2:\\\"id\\\";i:2;s:4:\\\"name\\\";s:4:\\\"Exam\\\";s:9:\\\"max_score\\\";i:60;}s:11:\\\"?*?original\\\";a:3:{s:2:\\\"id\\\";i:2;s:4:\\\"name\\\";s:4:\\\"Exam\\\";s:9:\\\"max_score\\\";i:60;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:0:{}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}s:5:\\\"blank\\\";b:1;}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1757840675, "scoreData_68c683f73f41e", "a:4:{s:8:\"students\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:4:{i:0;a:6:{s:2:\"id\";i:35;s:4:\"name\";s:21:\"<PERSON><PERSON><PERSON><PERSON> Olajumoke <PERSON>\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0109\";s:16:\"assessmentScores\";N;s:10:\"totalScore\";N;s:5:\"grade\";N;}i:1;a:6:{s:2:\"id\";i:36;s:4:\"name\";s:19:\"ADEYI TOYIN Rukayat\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0110\";s:16:\"assessmentScores\";N;s:10:\"totalScore\";N;s:5:\"grade\";N;}i:2;a:6:{s:2:\"id\";i:37;s:4:\"name\";s:23:\"KOLAWOLE Favour Deborah\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0113\";s:16:\"assessmentScores\";N;s:10:\"totalScore\";N;s:5:\"grade\";N;}i:3;a:6:{s:2:\"id\";i:38;s:4:\"name\";s:21:\"HAMMED Kazeem Olaitan\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0117\";s:16:\"assessmentScores\";N;s:10:\"totalScore\";N;s:5:\"grade\";N;}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}s:12:\"tableFilters\";a:5:{s:17:\"school_session_id\";s:1:\"3\";s:11:\"semester_id\";s:1:\"1\";s:8:\"level_id\";s:1:\"1\";s:13:\"department_id\";s:2:\"16\";s:9:\"course_id\";s:3:\"326\";}s:11:\"assessments\";O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:2:{i:0;O:21:\"App\\Models\\Assessment\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:11:\"assessments\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:1;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:3:{s:2:\"id\";i:1;s:4:\"name\";s:4:\"C.A.\";s:9:\"max_score\";i:40;}s:11:\"\u0000*\u0000original\";a:3:{s:2:\"id\";i:1;s:4:\"name\";s:4:\"C.A.\";s:9:\"max_score\";i:40;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:0:{}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:21:\"App\\Models\\Assessment\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:11:\"assessments\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:1;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:3:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Exam\";s:9:\"max_score\";i:60;}s:11:\"\u0000*\u0000original\";a:3:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Exam\";s:9:\"max_score\";i:60;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:0:{}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}s:5:\"blank\";b:1;}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 72}], "start": **********.266249, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "racoed", "explain": null, "start_percent": 44.985, "width_percent": 3.408}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 106}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.276834, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:39", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=39", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "39"}, "connection": "racoed", "explain": null, "start_percent": 48.394, "width_percent": 0}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": "view", "name": "filament.hooks.scoresheet-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.php", "line": 8}, {"index": 14, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.284288, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 48.394, "width_percent": 0.621}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": "view", "name": "filament.hooks.scoresheet-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.php", "line": 10}, {"index": 14, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.286647, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 49.014, "width_percent": 0.871}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '3' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.295948, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:259", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=259", "ajax": false, "filename": "ScoreResource.php", "line": "259"}, "connection": "racoed", "explain": null, "start_percent": 49.886, "width_percent": 1.318}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.29984, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:265", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=265", "ajax": false, "filename": "ScoreResource.php", "line": "265"}, "connection": "racoed", "explain": null, "start_percent": 51.203, "width_percent": 2.374}, {"sql": "select * from `levels` where `levels`.`id` = '1' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 271}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.304091, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:271", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 271}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=271", "ajax": false, "filename": "ScoreResource.php", "line": "271"}, "connection": "racoed", "explain": null, "start_percent": 53.577, "width_percent": 1.024}, {"sql": "select * from `courses` where `courses`.`id` = '326' limit 1", "type": "query", "params": [], "bindings": ["326"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 277}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.30672, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:277", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=277", "ajax": false, "filename": "ScoreResource.php", "line": "277"}, "connection": "racoed", "explain": null, "start_percent": 54.601, "width_percent": 0.621}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.313348, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 55.222, "width_percent": 0.958}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.3158872, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 56.18, "width_percent": 0.49}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.317838, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 56.67, "width_percent": 0.457}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.3202, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "racoed", "explain": null, "start_percent": 57.127, "width_percent": 1.111}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null order by `last_name` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.322834, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 58.238, "width_percent": 1.666}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 154}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.340384, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:154", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 154}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=154", "ajax": false, "filename": "ScoreResource.php", "line": "154"}, "connection": "racoed", "explain": null, "start_percent": 59.904, "width_percent": 0.588}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 165}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.351784, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:165", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=165", "ajax": false, "filename": "ScoreResource.php", "line": "165"}, "connection": "racoed", "explain": null, "start_percent": 60.492, "width_percent": 0.849}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 177}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.357311, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:177", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=177", "ajax": false, "filename": "ScoreResource.php", "line": "177"}, "connection": "racoed", "explain": null, "start_percent": 61.342, "width_percent": 0.544}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 193}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.362651, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:193", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=193", "ajax": false, "filename": "ScoreResource.php", "line": "193"}, "connection": "racoed", "explain": null, "start_percent": 61.886, "width_percent": 1.285}, {"sql": "select `code`, `id` from `courses` where `department_id` = '16' and `semester_id` = '1' and `level_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 216}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.370014, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:216", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=216", "ajax": false, "filename": "ScoreResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 63.171, "width_percent": 2.57}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.40588, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 65.741, "width_percent": 0.566}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.408168, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 66.307, "width_percent": 0.588}, {"sql": "select `score` from `scores` where (`registration_id` = 107 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [107, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.410933, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 66.895, "width_percent": 2.069}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.415993, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 68.964, "width_percent": 0.588}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.418, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 69.552, "width_percent": 0.512}, {"sql": "select `score` from `scores` where (`registration_id` = 107 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [107, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.419811, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 70.064, "width_percent": 0.512}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.422671, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 70.576, "width_percent": 0.566}, {"sql": "select `total` from `total_scores` where (`registration_id` = 107 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [107, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.4250689, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 71.142, "width_percent": 1.198}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.435701, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 72.34, "width_percent": 0.599}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.437693, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 72.939, "width_percent": 0.512}, {"sql": "select `score` from `scores` where (`registration_id` = 103 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [103, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.439495, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 73.451, "width_percent": 0.501}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.442276, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 73.952, "width_percent": 0.512}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.444154, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 74.464, "width_percent": 0.512}, {"sql": "select `score` from `scores` where (`registration_id` = 103 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [103, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.447129, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 74.975, "width_percent": 1.296}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.450823, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 76.271, "width_percent": 0.61}, {"sql": "select `total` from `total_scores` where (`registration_id` = 103 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [103, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.4527779, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 76.881, "width_percent": 0.446}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.461758, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 77.328, "width_percent": 0.632}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.464804, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 77.959, "width_percent": 0.653}, {"sql": "select `score` from `scores` where (`registration_id` = 115 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [115, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.466832, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 78.613, "width_percent": 0.468}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.469646, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 79.081, "width_percent": 0.468}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.471528, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 79.549, "width_percent": 0.479}, {"sql": "select `score` from `scores` where (`registration_id` = 115 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [115, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.473335, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 80.028, "width_percent": 0.555}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.476262, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 80.584, "width_percent": 0.512}, {"sql": "select `total` from `total_scores` where (`registration_id` = 115 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [115, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.478186, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 81.096, "width_percent": 0.446}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.487219, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 81.542, "width_percent": 0.566}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.4891841, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 82.108, "width_percent": 0.512}, {"sql": "select `score` from `scores` where (`registration_id` = 111 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [111, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.4910061, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 82.62, "width_percent": 0.479}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.4937742, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 83.099, "width_percent": 0.512}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.496641, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 83.611, "width_percent": 1.568}, {"sql": "select `score` from `scores` where (`registration_id` = 111 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [111, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.499572, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 85.179, "width_percent": 0.566}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.502546, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 85.745, "width_percent": 0.512}, {"sql": "select `total` from `total_scores` where (`registration_id` = 111 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [111, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.504382, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 86.257, "width_percent": 0.446}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 1 and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [1, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 15}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.518925, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "filament.hooks.global-portal-access-banner:15", "source": {"index": 14, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=15", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "15"}, "connection": "racoed", "explain": null, "start_percent": 86.704, "width_percent": 1.503}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.539898, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 88.206, "width_percent": 0.555}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.541946, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 88.762, "width_percent": 0.403}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.543829, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 89.165, "width_percent": 0.425}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.546816, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 89.589, "width_percent": 1.655}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.558134, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 91.245, "width_percent": 0.577}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.56019, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 91.822, "width_percent": 0.425}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.56239, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 92.247, "width_percent": 0.544}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.565379, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 92.791, "width_percent": 1.209}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.572404, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 94, "width_percent": 0.566}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.574447, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 94.566, "width_percent": 0.414}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.576328, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 94.98, "width_percent": 0.425}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.5786412, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 95.405, "width_percent": 2.211}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.584086, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 97.615, "width_percent": 0.555}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.586124, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 98.171, "width_percent": 0.381}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.587984, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 98.552, "width_percent": 0.403}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.590144, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 98.955, "width_percent": 1.045}]}, "models": {"data": {"App\\Models\\User": {"value": 33, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Department": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\Registration": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\Course": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Grade": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\Assessment": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FAssessment.php&line=1", "ajax": false, "filename": "Assessment.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Semester": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}}, "count": 99, "is_counter": true}, "livewire": {"data": {"app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores #MNLe4f2U0ij6IHzxC0Gq": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"score_filter\" => array:5 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n        \"course_id\" => \"326\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => []\n    \"defaultActionArguments\" => []\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"score_filter\" => array:5 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n        \"course_id\" => \"326\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores\"\n  \"component\" => \"App\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores\"\n  \"id\" => \"MNLe4f2U0ij6IHzxC0Gq\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores@mountAction<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:159-212</a>", "middleware": "web", "duration": "897ms", "peak_memory": "14MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1051943265 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1051943265\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-500655285 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZJVrHhXtjMqHCLzWyEkimkh54NxwTv9spk0SuWNB</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1928 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;score_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;,&quot;course_id&quot;:&quot;326&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:true,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;score_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;,&quot;course_id&quot;:&quot;326&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;MNLe4f2U0ij6IHzxC0Gq&quot;,&quot;name&quot;:&quot;app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores&quot;,&quot;path&quot;:&quot;staff\\/scores\\/scores&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;cf042b240d034d50edf0678e49b3d293c8ee3f758489b999cbbc18ee077dabfa&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">print_BlankScoresheet</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500655285\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1035112305 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkJqd2dSSjRkazZQbTF3NjJISW9aTHc9PSIsInZhbHVlIjoiZkJJS1MzeTRxWWxxMFVDbDNPQTZUSkIrZUxVYmZHR0F0NnNKYWlkQ0V6d2ZOaW9QRmo4eHp2UVJoY21KdVJGRVd3c2JaTW1vZHUvMWxyZUFqNmsvWFNZYU04d0ZKWEg2aHZZQytxNFlmV0xRMldjODVtVjl6bjRNSnpPMzlmaWYxT08zMHNIM1U4NGNvSkcrTmJYcGJKOXhxOTNDUXRDM0FGQkgwNGJHYmRmb2tuKzJYdnBoUEVaOEU2SG9UdG9ycE5xc2RSdFdKTDMrWUQrNGM5em1BQkdiT3BCY2xqeDVMVldGd2xQemhYUT0iLCJtYWMiOiI0NzM3Y2NiM2RhZGE2YTQ2MmJhNjAyY2UyZmNhZmRjOGZiZTRhMzg4ZjJkN2M2ODQzZWM4NjdkOTIyNzgwODQyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkpxSWVWa2xRWUhGNFVnSGxYakNhTVE9PSIsInZhbHVlIjoiYUtWdnlFODBSK2lWbmg5MXVFUkFtWjNZY05ha1pXTzZObjFvRUdWVmxpSCsyZHlmNFhQM2V5VDdkUmVjVHZrMFJudEtGVXJuUHhvdm1xS0tweUx3d3VTR29mbURHZm5MbGJKMlh0aFVUK1BWQjZOUlh0cSt0WTBXMmsxSlBtWkYiLCJtYWMiOiJiNzg2NTAyNjYwZTU5OWE0YWJkZjMwNTEyNjY5ZWUzYjg5MzQ4ZjNjYTI2ZWM4YTExZTAxZDJjNTk2NWIxMmU2IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6InhkOEovam1qTkJvUVRzZGxTa3BMSkE9PSIsInZhbHVlIjoibU41M0tlSmowTTY1RWxIYllOV213amNWVWpZN0xaKy9oemh3eDJ0TWhRUFRCaCt5VWFOZ1dpcHQvRVRHTmx2Vm5nTG9Ed1VlVWFHeWY0bWNiSkJPdGVOQ1dPMnZBbHhuWS82ZkdoU1BLdkpOTjBtaTZQVVZVNkN6K0JWUkQ0aEgiLCJtYWMiOiI5ZjU0M2I1ZGQ2MWQyNzg3NzU3OWM4NTMzNTkyNTA2YWUxYjk0ZWRkNzcyMmQ5NzRhMmJjOWZhZWJkOTQ0NTFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"262 characters\">https://portal.racoed.test/staff/scores/scores?tableFilters[score_filter][school_session_id]=3&amp;tableFilters[score_filter][semester_id]=1&amp;tableFilters[score_filter][level_id]=1&amp;tableFilters[score_filter][department_id]=16&amp;tableFilters[score_filter][course_id]=326</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2355</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035112305\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-463991630 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|hwcJ4OuuwQL0PF8lFxcDDwdyvAE0tEze6pcPYby2S5UPWNUUuLsx0ygIxRDO|$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZJVrHhXtjMqHCLzWyEkimkh54NxwTv9spk0SuWNB</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zuNniNVqlMeCDsAImSjdDg0J7ofFi7U7ZN0Quta9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463991630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1522360490 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 14 Sep 2025 08:59:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522360490\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-939659772 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZJVrHhXtjMqHCLzWyEkimkh54NxwTv9spk0SuWNB</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageScores_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>score_filter</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n        \"<span class=sf-dump-key>course_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">326</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"302 characters\">https://portal.racoed.test/staff/scores/scores?tableFilters%5Bscore_filter%5D%5Bschool_session_id%5D=3&amp;tableFilters%5Bscore_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Bscore_filter%5D%5Blevel_id%5D=1&amp;tableFilters%5Bscore_filter%5D%5Bdepartment_id%5D=16&amp;tableFilters%5Bscore_filter%5D%5Bcourse_id%5D=326</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-939659772\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}