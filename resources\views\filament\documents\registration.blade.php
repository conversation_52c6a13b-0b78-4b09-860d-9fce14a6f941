<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">   
    <title>Registration - {{ $student->name }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
           @media print {
            @page { size: A4; margin: 0mm; }
            .print\:hidden { display: none !important; }
            body { font-size: 10px; }
            h1 { font-size: 16px !important; }
            th, td { font-size: 10px !important; }
            .print-grid {
                    display: grid !important;
                    grid-template-columns: 1fr 1fr !important;
                    gap: 1rem;
                }
                th, tfoot tr {
                  background-color: #f9fafb !important; /* Tailwind's gray-50 */
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
              }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        {{-- HEADER --}}
        <x-document-header 
            :collegeLogo="asset('images/racoed-favicon.png')"
            :collegeName="$collegeSettings->name"
            :collegeMotto="$collegeSettings->motto"
            :collegeAddress="$collegeSettings->address"
            :collegePhone="$collegeSettings->phone"
            :collegeEmail="$collegeSettings->email"
            :studentPhoto="$student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')"
        /> 

        {{-- STUDENT & ACADEMIC DETAILS --}}
        <div class="border rounded p-1">
            <h2 class="text-center font-bold mb-2">Student & Academic Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2">
                <div class="border p-1"><strong>Name:</strong> {{ $student->name ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Gender:</strong> {{ $student->gender ?? 'NIL' }}</div>   
                <div class="border p-1"><strong>Phone:</strong> {{ $student->phone ?? 'NIL' }}</div>  
                <div class="border p-1"><strong>Email:</strong> {{ $student->email ?? 'NIL' }}</div>  
                <div class="border p-1 "><strong>Matric. no.:</strong> {{ $student->matric_number ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Session:</strong> {{ $registration?->SchoolSession->name ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Level:</strong> {{ $registration?->level->name ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Semester:</strong> {{ $registration?->semester->name ?? 'NIL' }}</div>
                <div class="border p-1 sm:col-span-2"><strong>Programme:</strong> {{ $registration?->programme->name ?? 'NIL' }}</div>                   
            </div>
        </div>

      {{-- REGISTERED COURSES --}}
        <div class="border rounded p-1">
            <h2 class="text-center font-bold mb-2">Registered Courses</h2>  

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs print-grid">
                @php
                    $levelId = $registration->level_id;
                    $semesterId = $registration->semester_id;

                    $firstDeptId = $registration->programme->first_department_id;
                    $secondDeptId = $registration->programme->second_department_id;

                    $educationDeptId = \App\Models\Department::where('is_edu', true)->value('id');
                    $gseDeptId = \App\Models\Department::where('is_gse', true)->value('id');

                    $allDeptIds = [$educationDeptId, $gseDeptId, $firstDeptId, $secondDeptId];

                    $courses = \App\Models\Course::whereIn('department_id', $allDeptIds)
                        ->where('level_id', $levelId)
                        ->where('semester_id', $semesterId)
                        ->get()
                        ->groupBy('department_id');
                @endphp

                @foreach ($allDeptIds as $deptId)
                    @php
                        $dept = \App\Models\Department::find($deptId);
                        $deptCourses = $courses->get($deptId, collect());
                    @endphp

                    @if ($dept)
                        <div>
                            <h3 class="font-semibold text-center mb-1">{{ $dept->name }} Courses</h3>
                            <table class="w-full border border-gray-300">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="border px-2 py-1 text-left">Code</th>
                                        <th class="border px-2 py-1 text-left">Title</th>
                                        <th class="border px-2 py-1 text-center">Credit</th>
                                        <th class="border px-2 py-1 text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($deptCourses as $course)
                                        <tr>
                                            <td class="border px-2 py-1">{{ $course->code }}</td>
                                            <td class="border px-2 py-1">{{ $course->title }}</td>
                                            <td class="border px-2 py-1 text-center">{{ $course->credit }}</td>
                                            <td class="border px-2 py-1 text-center">{{ $course->course_status->getAlias() }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="border px-2 py-2 text-center">No courses</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    @endif
                @endforeach
            </div>

        </div>

        {{-- SIGNATURES --}}
        @if ($headOfDepartment)           
        <p>
            <b>{{ $headOfDepartment?->title?->getLabel() ?? '' }} {{ $headOfDepartment?->name ?? '' }}{{ $headOfDepartment?->qualification ? '(' . collect($headOfDepartment->qualification)->map(fn($q) => \App\Enums\Qualification::from($q)->getLabel())->join(', ') . ')' : '' }}</b><br>
            <b>{{ $headOfDepartment?->role?->getLabel() ?? '' }}</b>

            <hr style="margin-top: 40px; border: 1px solid #000; width: 200px;">
            <p style="margin: 5px 0;">Sign & Date</p>

        </p>
        @endif

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print registration
            </x-filament::button>
        </div>

        <script>
            // Automatically open print dialog when the page loads
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>
