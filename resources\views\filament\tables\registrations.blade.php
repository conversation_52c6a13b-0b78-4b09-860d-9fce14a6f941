<div>
<table class="table-auto border w-full text-left mt-4">
    <thead>
        <tr class="bg-gray-100">
            <th class="border px-4 py-2 text-center" colspan="6">Registrations</th>
        </tr>
        <tr class="bg-gray-100">           
            <th class="border px-4 py-2">Session</th>
            <th class="border px-4 py-2">Semester</th>
            <th class="border px-4 py-2">Level</th>
            <th class="border px-4 py-2">Programme</th>
            <th class="border px-4 py-2">Active</th>
            <th class="border px-4 py-2">Action</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($registrations as $registration)
            <tr>
                <td class="border px-4 py-2">{{ $registration->schoolSession->name }}</td>
                <td class="border px-4 py-2">{{ $registration->semester->name }}</td>
                <td class="border px-4 py-2">{{ $registration->level->name }}</td>
                <td class="border px-4 py-2">{{ $registration->programme->name }}</td>                
                <td class="border px-4 py-2">
                    <livewire:registration-toggle :registration="$registration" :key="$registration->id" />
                </td>
                <td class="border px-4 py-2">
                    <x-filament::dropdown>
                        <x-slot name="trigger">                            
                            <x-filament::icon-button icon="heroicon-m-document-text" color="primary" label="Export registration"/>
                        </x-slot>
                        <x-filament::dropdown.list>
                            <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printRegistration({{ $registration->id }})">                                                              
                                    Print                              
                            </x-filament::dropdown.list.item>
                            <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadRegistration({{ $registration->id }})">                                                          
                                    Download                                
                            </x-filament::dropdown.list.item>
                        </x-filament::dropdown.list>
                    </x-filament::dropdown>               
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
</div>
