<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">   
    <title>Admission letter - {{ $student->name }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
           @media print {
            @page { size: A4; margin: 0mm; }
            .print\:hidden { display: none !important; }
            body { font-size: 10px; }
            h1 { font-size: 16px !important; }
            th, td { font-size: 10px !important; }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        {{-- HEADER --}}
        <x-document-header 
            :collegeLogo="asset('images/racoed-favicon.png')"
            :collegeName="$collegeSettings->name"
            :collegeMotto="$collegeSettings->motto"
            :collegeAddress="$collegeSettings->address"
            :collegePhone="$collegeSettings->phone"
            :collegeEmail="$collegeSettings->email"
            :studentPhoto="$student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')"
        /> 

        {{-- STUDENT ADDRESS --}}
        <div>
            <p class="leading-relaxed"><b>{{ $student->name ?? 'NIL' }}</b></p>
            <p class="leading-relaxed">{{ $student->address ?? 'NIL' }}</p>
            <i>{{ $student?->application?->admission_date?->format('d M, Y') ?? 'NIL' }}</i>
        </div>

        {{-- TITLE --}}
        <div class="flex items-center justify-center">
            <h1 class="font-bold">OFFER OF PROVISIONAL ADMISSION</h1>
        </div>

        {{-- CONTENT --}}
        <div>
            <p class="leading-relaxed text-base mb-4">Dear {{ $student->name ?? 'NIL' }},</p>

            <p class="leading-relaxed mb-4">
                We are pleased to inform you that your application to <b>{{ $collegeSettings->name ?? 'NIL' }}</b>, under the <b>Alhaji Amusa Wahabi Omotosho Foundation Tuition-Free Scholarship Scheme</b>, has been successful.
            </p>

            <p class="leading-relaxed mb-4">
                You have been offered <b>provisional admission</b> to study <b>{{ $student->application?->programme->name ?? 'NIL' }}</b> (Full-Time), a <b>six-semester</b> programme leading to the award of the <b>Nigeria Certificate in Education (NCE)</b> for the <b>{{ $student->application->schoolSession->name ?? 'NIL' }}</b> academic session.
            </p>

            <p class="leading-relaxed mb-4">
                Please note that your scholarship covers <b>100% tuition only</b>. You are required to pay <b>registration and other charges</b> as outlined in the attached payment schedule.
            </p>

            <p class="leading-relaxed mb-4">
                <b>Admission Conditions:</b>
                <br>1. Minimum of five O’Level credits in relevant subjects (max. 2 sittings).  
                <br>2. All submitted details must be accurate and verifiable.  
                <br>3. Present original credentials at registration.  
                <br>4. Admission will be withdrawn if found ineligible.  
                <br>5. Undergo the college’s medical test.  
                <br>6. Submit signed court undertaking.  
                <br>7. Pay applicable fees before deadline.  
                <br>8. No refunds under any circumstance.  
                <br>9. Must have written UTME and met all JAMB requirements.
            </p>

            <p class="leading-relaxed mb-4">
                <b>Note:</b> Your admission must be accepted via <b>JAMB CAPS</b>. This offer is valid for the current session only and cannot be deferred.
            </p>

            <p class="leading-relaxed mb-4"><b>Congratulations!</b> We look forward to having you on campus.</p>

            <p class="leading-relaxed mb-4">Yours sincerely,</p>
            {{-- SIGNATURES --}}
            @if ($registrar) 

            <div class="relative w-20 h-20 mt-4">
                <img src="{{ Storage::url($collegeSettings->stamp) }}" alt="Stamp" class="absolute inset-0 w-full h-full object-contain z-10">
                <img src="{{ Storage::url($collegeSettings->registrar_sign) }}" alt="Signature" class="absolute inset-0 w-full h-full object-contain z-0">
            </div>

            <p>
                <b>{{ $registrar?->title?->getLabel() ?? '' }} {{ $registrar?->name ?? '' }}{{ $registrar?->qualification ? '(' . collect($registrar->qualification)->map(fn($q) => \App\Enums\Qualification::from($q)->getLabel())->join(', ') . ')' : '' }}</b><br>
                <b>{{ $registrar?->role?->getLabel() ?? '' }}</b>
            </p>          
        </div>
        @endif

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print admission letter
            </x-filament::button>
        </div>

        <script>
            // Automatically open print dialog when the page loads
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>
