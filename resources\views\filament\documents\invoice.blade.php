<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">   
  <title>Invoice - {{ $invoice->user->name }}</title>
  @livewireStyles
  @filamentStyles
  @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])

  <style>   
    @media print {
            @page { size: A4; margin: 0mm; }
            .print\:hidden { display: none !important; }
            body { font-size: 10px; }
            h1 { font-size: 16px !important; }
            th, td { font-size: 10px !important; }
            th, tfoot tr {
                  background-color: #f9fafb !important; /* Tailwind's gray-50 */
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
              }
        }

    .watermark-text {
      font-size: 200px;
      font-weight: semibold;
      color: #D1D5DB;
      transform: rotate(-45deg);
      user-select: none;
    }
  </style>
</head>
  <body class="font-sans leading-relaxed p-4">

    {{-- Watermark --}}
    <div class="fixed top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none opacity-10 print:opacity-5 z-0">
      <div class="transform -rotate-45 text-gray-400 text-9xl font-bold">
        {{ strtoupper($invoice->invoice_status->getLabel() ?? 'NIL') }}
      </div>
    </div>

    {{-- Main Content --}}  
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">

    {{-- HEADER --}}
    <x-document-header 
    :collegeLogo="asset('images/racoed-favicon.png')"
    :collegeName="$collegeSettings->name"
    :collegeMotto="$collegeSettings->motto"
    :collegeAddress="$collegeSettings->address"
    :collegePhone="$collegeSettings->phone"
    :collegeEmail="$collegeSettings->email"
    :studentPhoto="$invoice->user->photo ? Storage::url($invoice->user->photo) : asset('images/placeholder.png')"
/> 

    {{-- TITLE AND META --}}
    <div class="text-center">
      <h1 class="text-3xl font-bold mb-2 underline">INVOICE</h1>
      <p class="text-gray-600 text-sm">
        <span class="font-semibold">Invoice no.:</span> {{ $invoice->number }} | <span class="font-semibold">Print date & time:</span> {{ now()->format('d M, Y h:i A') }}
      </p>
    </div>

    {{-- STUDENT & PAYMENT INFO --}}
    <div class="flex flex-col md:flex-row justify-between gap-4 mb-4 print:flex-row print:gap-4 print:justify-between">
        <div class="w-full md:w-1/2 print:w-1/2">
        <h3 class="text-gray-800 font-semibold mb-2">Student Details:</h3>
        <p class="text-gray-800 text-sm">
            <span class="font-semibold">Name:</span> {{ $invoice->user?->name ?? 'NIL' }}<br>
            <span class="font-semibold">Email:</span> {{ $invoice->user?->email ?? 'NIL' }}<br>
            <span class="font-semibold">Phone:</span> {{ $invoice->user?->phone ?? 'NIL' }}<br>
            <span class="font-semibold">Address:</span> {{ $invoice->user?->address ?? 'NIL' }}
        </p>
        </div>
        <div class="w-full md:w-1/2 print:w-1/2">
        <h3 class="text-gray-800 font-semibold mb-2">Payment Details:</h3>
        <p class="text-gray-800 text-sm">
            <span class="font-semibold">Method:</span> {{ $invoice->transaction?->payment_method?->getLabel() ?? 'NIL' }}<br>
            <span class="font-semibold">Status:</span> 
            <span class="font-semibold text-{{ $invoice->invoice_status->getTailwindColor() }}">
            {{ $invoice->invoice_status->getLabel() }}
            </span><br>
            <span class="font-semibold">Date:</span> {{ $invoice->paid_at ? $invoice->paid_at->format('d M, Y') : 'NIL' }}<br>
            <span class="font-semibold">Time:</span> {{ $invoice->paid_at ? $invoice->paid_at->format('h:i A') : 'NIL' }}<br>
            </p>
            </div>
    </div>
  

    {{-- TABLE --}}
    @php
    $isFee = $invoice->payable instanceof \App\Models\Fee;
    $feeCategory = $isFee ? $invoice->payable->feeCategory?->name : null;
    $descriptions = collect($isFee ? $invoice->payable->description : $invoice->description);
    $totalAmount = $isFee ? $invoice->payable->total_amount : $invoice->total_amount;
    @endphp

    <div class="mb-4">
        <h2 class="text-lg font-semibold mb-3 text-center underline">
            {{ Str::title($feeCategory ?? 'Fee Breakdown') }}
        </h2>

        <div class="overflow-x-auto">
            <table class="w-full border border-gray-400">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="text-left py-1 px-4 text-sm font-semibold border border-gray-400">Type</th>
                        <th class="text-left py-1 px-4 text-sm font-semibold border border-gray-400">Description</th>
                        <th class="text-left py-1 px-4 text-sm font-semibold border border-gray-400">Period</th>
                        <th class="text-right py-1 px-4 text-sm font-semibold border border-gray-400">Amount</th>
                    </tr>
                </thead>

                <tbody>
                    @if ($descriptions->isNotEmpty())
                        @foreach ($descriptions as $index => $item)
                            <tr class="{{ $index % 2 == 0 ? 'bg-white' : 'bg-gray-50' }}">
                                @if ($index === 0)
                                    <td class="py-1 px-4 text-sm border border-gray-400" rowspan="{{ $descriptions->count() }}">
                                        {{ $invoice->fee_type->getLabel() }}
                                    </td>
                                @endif
                                <td class="py-1 px-4 text-sm border border-gray-400">
                                    {{ is_array($item) ? $item['item'] : $item }}
                                </td>
                                <td class="py-1 px-4 text-sm border border-gray-400">
                                    <ol class="list-disc pl-4 space-y-1">
                                        @foreach((array) $invoice->period as $period)
                                            <li>{{ $period }}</li>
                                        @endforeach
                                    </ol>
                                </td>
                                <td class="py-1 px-4 text-sm text-right border border-gray-400">
                                    ₦{{ number_format(is_array($item) ? $item['amount'] : $invoice->total_amount, 0) }}
                                </td>
                            </tr>
                        @endforeach

                        <tfoot>
                            <tr class="bg-gray-50">
                                <td colspan="3" class="py-1 px-4 text-right font-semibold text-sm border border-gray-400">Total:</td>
                                <td class="py-1 px-4 text-right font-semibold text-sm border border-gray-400">₦{{ number_format($totalAmount, 0) }}</td>
                            </tr>
                            <tr class="bg-gray-50">
                                <td colspan="3" class="py-1 px-4 text-right font-semibold text-sm border border-gray-400">Paid:</td>
                                <td class="py-1 px-4 text-right font-semibold text-sm border border-gray-400">
                                    @if($invoice->invoice_status === \App\Enums\InvoiceStatus::PAID)
                                        ₦{{ number_format($invoice->total_amount, 0) }}
                                    @else
                                        NIL
                                    @endif
                                </td>
                            </tr>
                            @if($due !== null)
                                <tr class="bg-gray-50">
                                    <td colspan="3" class="py-1 px-4 text-right font-semibold text-sm border border-gray-400">
                                        <span class="text-xs text-gray-500 italic">Calculated after adding this payment to all past invoices — </span>
                                        <span class="italic">due:</span>
                                    </td>
                                    <td class="py-1 px-4 text-right font-semibold text-sm border border-gray-400 italic">₦{{ number_format($due ?? 0, 0) }}</td>
                                </tr>
                            @endif
                        </tfoot>
                    @endif
                </tbody>
            </table>
        </div>
    </div>


    {{-- FOOTER --}}
    <div class="text-left text-gray-600 text-sm border-t pt-4">
      <p class="font-semibold">Thank you for your payment!</p>

      <p>
        <b>{{ $bursar?->title?->getLabel() ?? '' }} {{ $bursar?->name ?? '' }} {{ $bursar?->qualification ? "({$bursar->qualification})" : '' }}
        </b><br>
        <b>{{ $bursar?->role?->getLabel() ?? '' }}</b>
    </p>

      <p class="mt-2">
        For support, please contact us:<br>
        Email: {{ $collegeSettings->email }} | Phone: {{ $collegeSettings->phone }}
      </p>
    </div>
    </div>

    {{-- PRINT BUTTON --}}
    <div class="fixed bottom-4 right-4 print:hidden">
      <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
        Print invoice
      </x-filament::button>
    </div>

    <script>
      window.onload = function() {
        window.print();
      };
    </script>
  </body>
  </html>
