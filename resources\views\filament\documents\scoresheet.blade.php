<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">
    <title>{{ $fileName }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
           @media print {
            @page { size: A4; margin: 0mm; }
             .college-name-print { font-size: 24pt !important; }
            .print\:hidden { display: none !important; }
            body { font-size: 10px; }
            h1 { font-size: 16px !important; }
            th, td { font-size: 10px !important; }
            .print-grid {
                    display: grid !important;
                    grid-template-columns: repeat(3, 1fr) !important;
                }
                th, tfoot tr {
                  background-color: #f9fafb !important; /* Tailwind's gray-50 */
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
              }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        {{-- HEADER --}}
        <x-document-simple-header 
            :collegeLogo="asset('images/racoed-favicon.png')"
            :collegeName="$collegeSettings->name"
            heading="Examination & Records Office"
            subheading="Result Scoresheet"
        /> 

        {{-- COURSE DETAILS --}}
        <div class="border  p-1">
            <h2 class="text-center font-bold mb-2">Course Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1 print-grid">
                <div class="border p-1"><strong>Session:</strong> {{ $session ?? 'NIL' }}</div>
                <div class="border p-1"><strong>Semester:</strong> {{ $semester ?? 'NIL' }}</div>   
                <div class="border p-1"><strong>Level:</strong> {{ $level ?? 'NIL' }}</div>  
                <div class="border p-1"><strong>Department:</strong> {{ $department ?? 'NIL' }}</div>  
                <div class="border p-1 sm:col-span-2">
                        <strong>Course:</strong>
                        @if($course)
                            {{ $course->code }}
                            {{ $course->credit ? ' | ' . $course->credit : '' }}
                            {{ $course->course_status ? $course->course_status->getAlias() : '' }}
                            {{ $course->title ? ' | ' . $course->title : '' }}
                        @else
                            NIL
                        @endif
                    </div>                 
            </div>
        </div>

        {{-- COURSE SCORES --}}
        <div class="border  p-1">
            <h2 class="text-center font-bold mb-2">Course Scores</h2>  
            
            <table class="w-full border border-gray-300 text-xs">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-2 py-1 text-center">#</th>
                        <th class="border px-2 py-1 text-left">Name</th>
                        <th class="border px-2 py-1 text-left">Matric. no.</th>
                        @foreach ($assessments as $assessment)
                            <th class="border px-2 py-1 text-center">
                                {{ $assessment->name }}<br>
                                <span class="block text-xs">({{ $assessment->max_score }})</span>
                            </th>
                        @endforeach
                        <th class="border px-2 py-1 text-center">Total<br><span class="block text-xs">({{ $assessments->sum('max_score') }})</span></th>
                        <th class="border px-2 py-1 text-center">Grade</th>
                    </tr>
                </thead>
                 <tbody>
                    @forelse ($students as $index => $student)
                        <tr>
                            <td class="border px-2 py-1 text-center">{{ $index + 1 }}</td>
                            <td class="border px-2 py-1 text-left">{{ $student['name'] }}</td>
                            <td class="border px-2 py-1 text-left">{{ $student['matric_number'] }}</td>

                            @foreach ($assessments as $assessment)
                                <td class="border px-2 py-1 text-center">
                                    @if($blank)
                                        {{-- leave empty --}}
                                    @else
                                        {{ $student['assessmentScores'][$assessment->name] ?? '-' }}
                                    @endif
                                </td>
                            @endforeach

                            <td class="border px-2 py-1 text-center">
                                @if($blank)
                                    {{-- leave empty --}}
                                @else
                                    {{ $student['totalScore'] ?? '-' }}
                                @endif
                            </td>

                            <td class="border px-2 py-1 text-center">
                                @if($blank)
                                    {{-- leave empty --}}
                                @else
                                    {{ $student['grade'] ?? '-' }}
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="{{ 4 + count($assessments) }}" class="border px-2 py-2 text-center">No students found</td>
                        </tr>
                    @endforelse
                </tbody>

            </table>
        </div>

        {{--SCORES SUMMARY --}}
        @if(!$blank && count($students) > 0)
            @php
                // Calculate summary statistics
                $totalStudents = count($students);
                $gradeStats = [];
                $passCount = 0;
                $failCount = 0;

                // Initialize grade counters
                $availableGrades = ['A', 'B', 'C', 'D', 'E', 'F'];
                foreach($availableGrades as $grade) {
                    $gradeStats[$grade] = 0;
                }

                // Count grades and pass/fail
                foreach($students as $student) {
                    $grade = $student['grade'] ?? 'F';
                    if(isset($gradeStats[$grade])) {
                        $gradeStats[$grade]++;
                    }

                    // Determine pass/fail (A, B, C, D, E are passing grades)
                    if(in_array($grade, ['A', 'B', 'C', 'D', 'E'])) {
                        $passCount++;
                    } else {
                        $failCount++;
                    }
                }

                // Calculate percentages
                $passPercentage = $totalStudents > 0 ? round(($passCount / $totalStudents) * 100, 1) : 0;
                $failPercentage = $totalStudents > 0 ? round(($failCount / $totalStudents) * 100, 1) : 0;
            @endphp

            <div class="border p-1">
                <h2 class="text-center font-bold mb-2">Scores Summary</h2>

                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1 print-grid">
                    <!-- Total Students -->
                    <div class="border p-1">
                        <strong>Total Students:</strong> {{ $totalStudents }}
                    </div>

                    <!-- Pass/Fail Statistics -->
                    <div class="border p-1">
                        <strong>Passed:</strong> {{ $passCount }} ({{ $passPercentage }}%)
                    </div>

                    <div class="border p-1">
                        <strong>Failed:</strong> {{ $failCount }} ({{ $failPercentage }}%)
                    </div>

                    <!-- Grade Distribution -->
                    @foreach($availableGrades as $grade)
                        @if($gradeStats[$grade] > 0)
                            <div class="border p-1">
                                <strong>Grade {{ $grade }}:</strong> {{ $gradeStats[$grade] }}
                                ({{ $totalStudents > 0 ? round(($gradeStats[$grade] / $totalStudents) * 100, 1) : 0 }}%)
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        @endif

      {{-- LECTURER IN CHARGE--}}
        <div class="mt-6">
            <p class="font-bold">Lecturer in Charge</p>
            <p><span class="font-bold">Name:</span> ____________________________________</p>
            <p><span class="font-bold">Sign & Date:</span> _____________________________</p>
        </div>

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print scoresheet
            </x-filament::button>
        </div>

        <script>
            // Automatically open print dialog when the page loads
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>

