{"__meta": {"id": "01K54Z05MBR0GKA0015RXXGKCW", "datetime": "2025-09-14 20:34:55", "utime": **********.243904, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.433521, "end": **********.243933, "duration": 1.8104119300842285, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": **********.433521, "relative_start": 0, "end": **********.896648, "relative_end": **********.896648, "duration": 0.*****************, "duration_str": "463ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.896702, "relative_start": 0.****************, "end": **********.243937, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.909138, "relative_start": 0.****************, "end": **********.910321, "relative_end": **********.910321, "duration": 0.0011830329895019531, "duration_str": "1.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.hooks.scoresheet-banner", "start": **********.384171, "relative_start": 0.****************, "end": **********.384171, "relative_end": **********.384171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0e562057e53b176eb249937b70a802f8", "start": **********.403084, "relative_start": 0.****************, "end": **********.403084, "relative_end": **********.403084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.506849, "relative_start": 1.0733280181884766, "end": **********.506849, "relative_end": **********.506849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.53238, "relative_start": 1.0988590717315674, "end": **********.53238, "relative_end": **********.53238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.564042, "relative_start": 1.1305210590362549, "end": **********.564042, "relative_end": **********.564042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.584354, "relative_start": 1.1508328914642334, "end": **********.584354, "relative_end": **********.584354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.604389, "relative_start": 1.170867919921875, "end": **********.604389, "relative_end": **********.604389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.614725, "relative_start": 1.181204080581665, "end": **********.614725, "relative_end": **********.614725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.021136, "relative_start": 1.5876150131225586, "end": **********.021136, "relative_end": **********.021136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.027786, "relative_start": 1.5942649841308594, "end": **********.027786, "relative_end": **********.027786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3e7af8a9f04a41a7583e36fe677e9b1b", "start": **********.034625, "relative_start": 1.6011040210723877, "end": **********.034625, "relative_end": **********.034625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.045855, "relative_start": 1.6123340129852295, "end": **********.045855, "relative_end": **********.045855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ba9cccfb20917de418a9fa74743df081", "start": **********.059106, "relative_start": 1.6255850791931152, "end": **********.059106, "relative_end": **********.059106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.086919, "relative_start": 1.653398036956787, "end": **********.086919, "relative_end": **********.086919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.113517, "relative_start": 1.6799960136413574, "end": **********.113517, "relative_end": **********.113517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ba9cccfb20917de418a9fa74743df081", "start": **********.137709, "relative_start": 1.7041878700256348, "end": **********.137709, "relative_end": **********.137709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.176899, "relative_start": 1.743377923965454, "end": **********.176899, "relative_end": **********.176899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.209845, "relative_start": 1.7763240337371826, "end": **********.209845, "relative_end": **********.209845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.240763, "relative_start": 1.8072419166564941, "end": **********.242712, "relative_end": **********.242712, "duration": 0.0019490718841552734, "duration_str": "1.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7975632, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 18, "nb_templates": 18, "templates": [{"name": "filament.hooks.scoresheet-banner", "param_count": null, "params": [], "start": **********.384083, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.phpfilament.hooks.scoresheet-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fscoresheet-banner.blade.php&line=1", "ajax": false, "filename": "scoresheet-banner.blade.php", "line": "?"}}, {"name": "__components::0e562057e53b176eb249937b70a802f8", "param_count": null, "params": [], "start": **********.402956, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/0e562057e53b176eb249937b70a802f8.blade.php__components::0e562057e53b176eb249937b70a802f8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F0e562057e53b176eb249937b70a802f8.blade.php&line=1", "ajax": false, "filename": "0e562057e53b176eb249937b70a802f8.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.506733, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.532239, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.56386, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.584234, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.604262, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.614632, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.021011, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.027658, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::3e7af8a9f04a41a7583e36fe677e9b1b", "param_count": null, "params": [], "start": **********.034501, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/3e7af8a9f04a41a7583e36fe677e9b1b.blade.php__components::3e7af8a9f04a41a7583e36fe677e9b1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F3e7af8a9f04a41a7583e36fe677e9b1b.blade.php&line=1", "ajax": false, "filename": "3e7af8a9f04a41a7583e36fe677e9b1b.blade.php", "line": "?"}}, {"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.045614, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::ba9cccfb20917de418a9fa74743df081", "param_count": null, "params": [], "start": **********.058948, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/ba9cccfb20917de418a9fa74743df081.blade.php__components::ba9cccfb20917de418a9fa74743df081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fba9cccfb20917de418a9fa74743df081.blade.php&line=1", "ajax": false, "filename": "ba9cccfb20917de418a9fa74743df081.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.086786, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.11304, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::ba9cccfb20917de418a9fa74743df081", "param_count": null, "params": [], "start": **********.137494, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/ba9cccfb20917de418a9fa74743df081.blade.php__components::ba9cccfb20917de418a9fa74743df081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fba9cccfb20917de418a9fa74743df081.blade.php&line=1", "ajax": false, "filename": "ba9cccfb20917de418a9fa74743df081.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.176682, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.20962, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}]}, "queries": {"count": 122, "nb_statements": 119, "nb_visible_statements": 122, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.18841000000000002, "accumulated_duration_str": "188ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 19 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.91908, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'tNqsA2mAWGTQPjNcsrbHj7I5nIy2ikyq9DNmXps1' limit 1", "type": "query", "params": [], "bindings": ["tNqsA2mAWGTQPjNcsrbHj7I5nIy2ikyq9DNmXps1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.920358, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 1.789}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.940896, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 1.789, "width_percent": 0.536}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 64}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.965334, "duration": 0.01582, "duration_str": "15.82ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:64", "source": {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=64", "ajax": false, "filename": "ScoreResource.php", "line": "64"}, "connection": "racoed", "explain": null, "start_percent": 2.325, "width_percent": 8.397}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.986614, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:67", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=67", "ajax": false, "filename": "ScoreResource.php", "line": "67"}, "connection": "racoed", "explain": null, "start_percent": 10.721, "width_percent": 0.425}, {"sql": "select `id`, `name`, `max_score` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 300}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}], "start": **********.9952142, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:300", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 300}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=300", "ajax": false, "filename": "ScoreResource.php", "line": "300"}, "connection": "racoed", "explain": null, "start_percent": 11.146, "width_percent": 0.796}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.009841, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 11.942, "width_percent": 0.547}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.01593, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 12.489, "width_percent": 0.541}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-09-14' and date(`semester_end`) >= '2025-09-14' limit 1", "type": "query", "params": [], "bindings": [3, "2025-09-14", "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.021402, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 13.03, "width_percent": 0.844}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.036156, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 13.874, "width_percent": 0.786}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.042675, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 14.66, "width_percent": 0.387}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.0497, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 15.047, "width_percent": 0.961}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 113}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.0585551, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:113", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=113", "ajax": false, "filename": "ManageScores.php", "line": "113"}, "connection": "racoed", "explain": null, "start_percent": 16.008, "width_percent": 1.661}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.07169, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 17.669, "width_percent": 0.515}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.077384, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 18.184, "width_percent": 0.467}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.084969, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 18.651, "width_percent": 0.584}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 113}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.117049, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:113", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=113", "ajax": false, "filename": "ManageScores.php", "line": "113"}, "connection": "racoed", "explain": null, "start_percent": 19.235, "width_percent": 1.683}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 82}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.133553, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:30", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=30", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "30"}, "connection": "racoed", "explain": null, "start_percent": 20.917, "width_percent": 0}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.135998, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 20.917, "width_percent": 0.467}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.1421142, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 21.384, "width_percent": 0.435}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.149843, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 21.819, "width_percent": 0.478}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}], "start": **********.156842, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:54", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=54", "ajax": false, "filename": "ManageScores.php", "line": "54"}, "connection": "racoed", "explain": null, "start_percent": 22.297, "width_percent": 1.55}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 144}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.166197, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 23.847, "width_percent": 1.003}, {"sql": "select * from `scores` where (`registration_id` = 107 and `course_id` = '326')", "type": "query", "params": [], "bindings": [107, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.173784, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 24.85, "width_percent": 2.877}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 26, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.183536, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 27.727, "width_percent": 0.478}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.188746, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 28.204, "width_percent": 0.525}, {"sql": "select `total` from `total_scores` where (`registration_id` = 107 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [107, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.195065, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 28.73, "width_percent": 1.306}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}], "start": **********.201011, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 30.036, "width_percent": 0.541}, {"sql": "select `total` from `total_scores` where (`registration_id` = 107 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [107, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 25, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.2064111, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 30.577, "width_percent": 0.531}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.2119699, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:169", "source": {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=169", "ajax": false, "filename": "ManageScores.php", "line": "169"}, "connection": "racoed", "explain": null, "start_percent": 31.108, "width_percent": 0.494}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 144}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.217774, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 31.601, "width_percent": 0.6}, {"sql": "select * from `scores` where (`registration_id` = 103 and `course_id` = '326')", "type": "query", "params": [], "bindings": [103, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.2231328, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 32.201, "width_percent": 0.786}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 26, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.229336, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 32.987, "width_percent": 0.594}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.233378, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 33.581, "width_percent": 0.563}, {"sql": "select `total` from `total_scores` where (`registration_id` = 103 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [103, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.238979, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 34.144, "width_percent": 0.525}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}], "start": **********.2444482, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 34.669, "width_percent": 0.52}, {"sql": "select `total` from `total_scores` where (`registration_id` = 103 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [103, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 25, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.2501981, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 35.189, "width_percent": 0.552}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.2555442, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:169", "source": {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=169", "ajax": false, "filename": "ManageScores.php", "line": "169"}, "connection": "racoed", "explain": null, "start_percent": 35.741, "width_percent": 0.425}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 144}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.260999, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 36.166, "width_percent": 0.536}, {"sql": "select * from `scores` where (`registration_id` = 115 and `course_id` = '326')", "type": "query", "params": [], "bindings": [115, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.267044, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 36.702, "width_percent": 0.648}, {"sql": "select * from `assessments` where `assessments`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 26, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.272959, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 37.349, "width_percent": 0.441}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.27828, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 37.79, "width_percent": 0.637}, {"sql": "select `total` from `total_scores` where (`registration_id` = 115 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [115, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.284004, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 38.427, "width_percent": 0.642}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}], "start": **********.2896512, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 39.069, "width_percent": 0.515}, {"sql": "select `total` from `total_scores` where (`registration_id` = 115 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [115, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 25, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.295648, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 39.584, "width_percent": 0.61}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.301222, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:169", "source": {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=169", "ajax": false, "filename": "ManageScores.php", "line": "169"}, "connection": "racoed", "explain": null, "start_percent": 40.194, "width_percent": 0.462}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 144}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.3069332, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 40.656, "width_percent": 0.706}, {"sql": "select * from `scores` where (`registration_id` = 111 and `course_id` = '326')", "type": "query", "params": [], "bindings": [111, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.3144789, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 41.362, "width_percent": 0.674}, {"sql": "select * from `assessments` where `assessments`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 64}, {"index": 26, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.320548, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:153", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=153", "ajax": false, "filename": "ManageScores.php", "line": "153"}, "connection": "racoed", "explain": null, "start_percent": 42.036, "width_percent": 0.393}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.3254201, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 42.429, "width_percent": 0.441}, {"sql": "select `total` from `total_scores` where (`registration_id` = 111 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [111, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 65}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.3313189, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 42.869, "width_percent": 0.488}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}], "start": **********.336525, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 43.358, "width_percent": 0.478}, {"sql": "select `total` from `total_scores` where (`registration_id` = 111 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [111, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 161}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 166}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 25, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}], "start": **********.341448, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 43.835, "width_percent": 0.382}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 66}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 59}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.346245, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:169", "source": {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=169", "ajax": false, "filename": "ManageScores.php", "line": "169"}, "connection": "racoed", "explain": null, "start_percent": 44.217, "width_percent": 0.525}, {"sql": "select `id`, `name`, `max_score` from `assessments` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 98}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}], "start": **********.3507922, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ManageScores.php:69", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=69", "ajax": false, "filename": "ManageScores.php", "line": "69"}, "connection": "racoed", "explain": null, "start_percent": 44.743, "width_percent": 0.43}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1757882394, 'scoreData_68c726ee56d18', 'a:4:{s:8:\\\"students\\\";O:29:\\\"Illuminate\\Support\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:4:{i:1;a:6:{s:2:\\\"id\\\";i:36;s:4:\\\"name\\\";s:19:\\\"ADEYI TOYIN Rukayat\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0110\\\";s:16:\\\"assessmentScores\\\";a:2:{s:4:\\\"C.A.\\\";i:10;s:4:\\\"Exam\\\";i:40;}s:10:\\\"totalScore\\\";i:50;s:5:\\\"grade\\\";s:1:\\\"C\\\";}i:0;a:6:{s:2:\\\"id\\\";i:35;s:4:\\\"name\\\";s:21:\\\"AJAY<PERSON> Olaju<PERSON>\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0109\\\";s:16:\\\"assessmentScores\\\";a:2:{s:4:\\\"C.A.\\\";i:22;s:4:\\\"Exam\\\";i:42;}s:10:\\\"totalScore\\\";i:64;s:5:\\\"grade\\\";s:1:\\\"B\\\";}i:3;a:6:{s:2:\\\"id\\\";i:38;s:4:\\\"name\\\";s:21:\\\"HAMMED Kazeem Olaitan\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0117\\\";s:16:\\\"assessmentScores\\\";a:2:{s:4:\\\"C.A.\\\";i:12;s:4:\\\"Exam\\\";i:45;}s:10:\\\"totalScore\\\";i:57;s:5:\\\"grade\\\";s:1:\\\"C\\\";}i:2;a:6:{s:2:\\\"id\\\";i:37;s:4:\\\"name\\\";s:23:\\\"KOLAWOLE Favour Deborah\\\";s:13:\\\"matric_number\\\";s:18:\\\"RACOED/22/BED/0113\\\";s:16:\\\"assessmentScores\\\";a:1:{s:4:\\\"Exam\\\";i:22;}s:10:\\\"totalScore\\\";i:22;s:5:\\\"grade\\\";s:1:\\\"F\\\";}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}s:12:\\\"tableFilters\\\";a:5:{s:17:\\\"school_session_id\\\";s:1:\\\"3\\\";s:11:\\\"semester_id\\\";s:1:\\\"1\\\";s:8:\\\"level_id\\\";s:1:\\\"1\\\";s:13:\\\"department_id\\\";s:2:\\\"16\\\";s:9:\\\"course_id\\\";s:3:\\\"326\\\";}s:11:\\\"assessments\\\";O:39:\\\"Illuminate\\Database\\Eloquent\\Collection\\\":2:{s:8:\\\"?*?items\\\";a:2:{i:0;O:21:\\\"App\\Models\\Assessment\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:11:\\\"assessments\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:1;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:3:{s:2:\\\"id\\\";i:1;s:4:\\\"name\\\";s:4:\\\"C.A.\\\";s:9:\\\"max_score\\\";i:40;}s:11:\\\"?*?original\\\";a:3:{s:2:\\\"id\\\";i:1;s:4:\\\"name\\\";s:4:\\\"C.A.\\\";s:9:\\\"max_score\\\";i:40;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:0:{}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}i:1;O:21:\\\"App\\Models\\Assessment\\\":33:{s:13:\\\"?*?connection\\\";s:5:\\\"mysql\\\";s:8:\\\"?*?table\\\";s:11:\\\"assessments\\\";s:13:\\\"?*?primaryKey\\\";s:2:\\\"id\\\";s:10:\\\"?*?keyType\\\";s:3:\\\"int\\\";s:12:\\\"incrementing\\\";b:1;s:7:\\\"?*?with\\\";a:0:{}s:12:\\\"?*?withCount\\\";a:0:{}s:19:\\\"preventsLazyLoading\\\";b:1;s:10:\\\"?*?perPage\\\";i:15;s:6:\\\"exists\\\";b:1;s:18:\\\"wasRecentlyCreated\\\";b:0;s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;s:13:\\\"?*?attributes\\\";a:3:{s:2:\\\"id\\\";i:2;s:4:\\\"name\\\";s:4:\\\"Exam\\\";s:9:\\\"max_score\\\";i:60;}s:11:\\\"?*?original\\\";a:3:{s:2:\\\"id\\\";i:2;s:4:\\\"name\\\";s:4:\\\"Exam\\\";s:9:\\\"max_score\\\";i:60;}s:10:\\\"?*?changes\\\";a:0:{}s:11:\\\"?*?previous\\\";a:0:{}s:8:\\\"?*?casts\\\";a:0:{}s:17:\\\"?*?classCastCache\\\";a:0:{}s:21:\\\"?*?attributeCastCache\\\";a:0:{}s:13:\\\"?*?dateFormat\\\";N;s:10:\\\"?*?appends\\\";a:0:{}s:19:\\\"?*?dispatchesEvents\\\";a:0:{}s:14:\\\"?*?observables\\\";a:0:{}s:12:\\\"?*?relations\\\";a:0:{}s:10:\\\"?*?touches\\\";a:0:{}s:27:\\\"?*?relationAutoloadCallback\\\";N;s:26:\\\"?*?relationAutoloadContext\\\";N;s:10:\\\"timestamps\\\";b:1;s:13:\\\"usesUniqueIds\\\";b:0;s:9:\\\"?*?hidden\\\";a:0:{}s:10:\\\"?*?visible\\\";a:0:{}s:11:\\\"?*?fillable\\\";a:0:{}s:10:\\\"?*?guarded\\\";a:1:{i:0;s:1:\\\"*\\\";}}}s:28:\\\"?*?escapeWhenCastingToString\\\";b:0;}s:5:\\\"blank\\\";b:0;}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1757882394, "scoreData_68c726ee56d18", "a:4:{s:8:\"students\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:4:{i:1;a:6:{s:2:\"id\";i:36;s:4:\"name\";s:19:\"ADEYI TOYIN Rukayat\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0110\";s:16:\"assessmentScores\";a:2:{s:4:\"C.A.\";i:10;s:4:\"Exam\";i:40;}s:10:\"totalScore\";i:50;s:5:\"grade\";s:1:\"C\";}i:0;a:6:{s:2:\"id\";i:35;s:4:\"name\";s:21:\"<PERSON><PERSON><PERSON><PERSON> Olajumo<PERSON>\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0109\";s:16:\"assessmentScores\";a:2:{s:4:\"C.A.\";i:22;s:4:\"Exam\";i:42;}s:10:\"totalScore\";i:64;s:5:\"grade\";s:1:\"B\";}i:3;a:6:{s:2:\"id\";i:38;s:4:\"name\";s:21:\"HAMMED Kazeem Olaitan\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0117\";s:16:\"assessmentScores\";a:2:{s:4:\"C.A.\";i:12;s:4:\"Exam\";i:45;}s:10:\"totalScore\";i:57;s:5:\"grade\";s:1:\"C\";}i:2;a:6:{s:2:\"id\";i:37;s:4:\"name\";s:23:\"KOLAWOLE Favour Deborah\";s:13:\"matric_number\";s:18:\"RACOED/22/BED/0113\";s:16:\"assessmentScores\";a:1:{s:4:\"Exam\";i:22;}s:10:\"totalScore\";i:22;s:5:\"grade\";s:1:\"F\";}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}s:12:\"tableFilters\";a:5:{s:17:\"school_session_id\";s:1:\"3\";s:11:\"semester_id\";s:1:\"1\";s:8:\"level_id\";s:1:\"1\";s:13:\"department_id\";s:2:\"16\";s:9:\"course_id\";s:3:\"326\";}s:11:\"assessments\";O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\u0000*\u0000items\";a:2:{i:0;O:21:\"App\\Models\\Assessment\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:11:\"assessments\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:1;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:3:{s:2:\"id\";i:1;s:4:\"name\";s:4:\"C.A.\";s:9:\"max_score\";i:40;}s:11:\"\u0000*\u0000original\";a:3:{s:2:\"id\";i:1;s:4:\"name\";s:4:\"C.A.\";s:9:\"max_score\";i:40;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:0:{}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:21:\"App\\Models\\Assessment\":33:{s:13:\"\u0000*\u0000connection\";s:5:\"mysql\";s:8:\"\u0000*\u0000table\";s:11:\"assessments\";s:13:\"\u0000*\u0000primaryKey\";s:2:\"id\";s:10:\"\u0000*\u0000keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\u0000*\u0000with\";a:0:{}s:12:\"\u0000*\u0000withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:1;s:10:\"\u0000*\u0000perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;s:13:\"\u0000*\u0000attributes\";a:3:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Exam\";s:9:\"max_score\";i:60;}s:11:\"\u0000*\u0000original\";a:3:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Exam\";s:9:\"max_score\";i:60;}s:10:\"\u0000*\u0000changes\";a:0:{}s:11:\"\u0000*\u0000previous\";a:0:{}s:8:\"\u0000*\u0000casts\";a:0:{}s:17:\"\u0000*\u0000classCastCache\";a:0:{}s:21:\"\u0000*\u0000attributeCastCache\";a:0:{}s:13:\"\u0000*\u0000dateFormat\";N;s:10:\"\u0000*\u0000appends\";a:0:{}s:19:\"\u0000*\u0000dispatchesEvents\";a:0:{}s:14:\"\u0000*\u0000observables\";a:0:{}s:12:\"\u0000*\u0000relations\";a:0:{}s:10:\"\u0000*\u0000touches\";a:0:{}s:27:\"\u0000*\u0000relationAutoloadCallback\";N;s:26:\"\u0000*\u0000relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\u0000*\u0000hidden\";a:0:{}s:10:\"\u0000*\u0000visible\";a:0:{}s:11:\"\u0000*\u0000fillable\";a:0:{}s:10:\"\u0000*\u0000guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\u0000*\u0000escapeWhenCastingToString\";b:0;}s:5:\"blank\";b:0;}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 74}], "start": **********.358778, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "racoed", "explain": null, "start_percent": 45.173, "width_percent": 1.943}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, {"index": 10, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 106}, {"index": 11, "namespace": null, "name": "vendor/filament/actions/src/Concerns/InteractsWithActions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\InteractsWithActions.php", "line": 204}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.376639, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CanUseDatabaseTransactions.php:39", "source": {"index": 9, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanUseDatabaseTransactions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanUseDatabaseTransactions.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FCanUseDatabaseTransactions.php&line=39", "ajax": false, "filename": "CanUseDatabaseTransactions.php", "line": "39"}, "connection": "racoed", "explain": null, "start_percent": 47.115, "width_percent": 0}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": "view", "name": "filament.hooks.scoresheet-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.php", "line": 8}, {"index": 14, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.385244, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 47.115, "width_percent": 0.478}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": "view", "name": "filament.hooks.scoresheet-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.php", "line": 10}, {"index": 14, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3892531, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 47.593, "width_percent": 0.334}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '3' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.4079301, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:259", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=259", "ajax": false, "filename": "ScoreResource.php", "line": "259"}, "connection": "racoed", "explain": null, "start_percent": 47.927, "width_percent": 0.552}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.4140408, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:265", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=265", "ajax": false, "filename": "ScoreResource.php", "line": "265"}, "connection": "racoed", "explain": null, "start_percent": 48.479, "width_percent": 0.499}, {"sql": "select * from `levels` where `levels`.`id` = '1' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 271}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.419001, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:271", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 271}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=271", "ajax": false, "filename": "ScoreResource.php", "line": "271"}, "connection": "racoed", "explain": null, "start_percent": 48.978, "width_percent": 0.435}, {"sql": "select * from `courses` where `courses`.`id` = '326' limit 1", "type": "query", "params": [], "bindings": ["326"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 277}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.424861, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:277", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=277", "ajax": false, "filename": "ScoreResource.php", "line": "277"}, "connection": "racoed", "explain": null, "start_percent": 49.414, "width_percent": 0.605}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.437584, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 50.019, "width_percent": 0.478}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.443473, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 50.496, "width_percent": 0.467}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.449902, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 50.963, "width_percent": 0.483}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.456679, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "racoed", "explain": null, "start_percent": 51.446, "width_percent": 1.269}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null order by `last_name` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.466061, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 52.715, "width_percent": 1.651}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 154}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.495435, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:154", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 154}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=154", "ajax": false, "filename": "ScoreResource.php", "line": "154"}, "connection": "racoed", "explain": null, "start_percent": 54.365, "width_percent": 0.525}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 165}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.521939, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:165", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=165", "ajax": false, "filename": "ScoreResource.php", "line": "165"}, "connection": "racoed", "explain": null, "start_percent": 54.891, "width_percent": 0.515}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 177}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.540119, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:177", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=177", "ajax": false, "filename": "ScoreResource.php", "line": "177"}, "connection": "racoed", "explain": null, "start_percent": 55.406, "width_percent": 0.494}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 193}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.572586, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:193", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=193", "ajax": false, "filename": "ScoreResource.php", "line": "193"}, "connection": "racoed", "explain": null, "start_percent": 55.899, "width_percent": 0.998}, {"sql": "select `code`, `id` from `courses` where `department_id` = '16' and `semester_id` = '1' and `level_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 216}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.594635, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:216", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=216", "ajax": false, "filename": "ScoreResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 56.897, "width_percent": 1.603}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.6761072, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 58.5, "width_percent": 0.441}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.681421, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 58.941, "width_percent": 0.547}, {"sql": "select `score` from `scores` where (`registration_id` = 107 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [107, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.685699, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 59.487, "width_percent": 0.711}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.692597, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 60.199, "width_percent": 0.398}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.6978521, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 60.597, "width_percent": 0.632}, {"sql": "select `score` from `scores` where (`registration_id` = 107 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [107, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.70216, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 61.228, "width_percent": 0.472}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.708568, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 61.701, "width_percent": 0.472}, {"sql": "select `total` from `total_scores` where (`registration_id` = 107 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [107, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.713122, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 62.173, "width_percent": 1.04}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.7365012, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 63.213, "width_percent": 0.425}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.740497, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 63.638, "width_percent": 0.425}, {"sql": "select `score` from `scores` where (`registration_id` = 103 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [103, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.744344, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 64.062, "width_percent": 0.488}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.75234, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 64.551, "width_percent": 0.425}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.756475, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 64.975, "width_percent": 0.414}, {"sql": "select `score` from `scores` where (`registration_id` = 103 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [103, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.7602909, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 65.389, "width_percent": 0.494}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.7694778, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 65.883, "width_percent": 0.6}, {"sql": "select `total` from `total_scores` where (`registration_id` = 103 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [103, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.774665, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 66.483, "width_percent": 0.552}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.812723, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 67.035, "width_percent": 2.075}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.824009, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 69.11, "width_percent": 0.77}, {"sql": "select `score` from `scores` where (`registration_id` = 115 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [115, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.832943, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 69.88, "width_percent": 1.226}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.844026, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 71.106, "width_percent": 0.626}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.85338, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 71.732, "width_percent": 0.918}, {"sql": "select `score` from `scores` where (`registration_id` = 115 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [115, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.860088, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 72.65, "width_percent": 0.812}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.874916, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 73.462, "width_percent": 0.727}, {"sql": "select `total` from `total_scores` where (`registration_id` = 115 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [115, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.8852031, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 74.189, "width_percent": 0.701}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.928982, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 74.89, "width_percent": 1.046}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.937175, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 75.935, "width_percent": 0.738}, {"sql": "select `score` from `scores` where (`registration_id` = 111 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [111, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.9437768, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 76.673, "width_percent": 0.674}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.956273, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 77.347, "width_percent": 0.642}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.963644, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 77.989, "width_percent": 1.327}, {"sql": "select `score` from `scores` where (`registration_id` = 111 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [111, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.9714992, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 79.316, "width_percent": 0.706}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.983857, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.022, "width_percent": 0.77}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986475, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.792, "width_percent": 0.568}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.024522, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.36, "width_percent": 1.274}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.071166, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.634, "width_percent": 0.669}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.073307, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.302, "width_percent": 0.472}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.075201, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.775, "width_percent": 0.462}, {"sql": "select * from `users` where `role` = ? and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = ?) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ? and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = ? or `second_department_id` = ?))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.079257, "duration": 0.0051600000000000005, "duration_str": "5.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.237, "width_percent": 2.739}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.100742, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.975, "width_percent": 0.632}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.102798, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.607, "width_percent": 0.462}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.104659, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.069, "width_percent": 0.462}, {"sql": "select * from `users` where `role` = ? and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = ?) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ? and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = ? or `second_department_id` = ?))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.108061, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.53, "width_percent": 1.64}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.15586, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.17, "width_percent": 0.786}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.158971, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.956, "width_percent": 0.658}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.16246, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.614, "width_percent": 0.807}, {"sql": "select * from `users` where `role` = ? and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = ?) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ? and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = ? or `second_department_id` = ?))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.169405, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.421, "width_percent": 2.691}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1885061, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.112, "width_percent": 0.812}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.191698, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.924, "width_percent": 0.663}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.195213, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.587, "width_percent": 0.658}, {"sql": "select * from `users` where `role` = ? and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = ?) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ? and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = ? or `second_department_id` = ?))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.202238, "duration": 0.00519, "duration_str": "5.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.245, "width_percent": 2.755}]}, "models": {"data": {"App\\Models\\User": {"value": 33, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Grade": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\Department": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\Registration": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\Score": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FScore.php&line=1", "ajax": false, "filename": "Score.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}, "App\\Models\\Assessment": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FAssessment.php&line=1", "ajax": false, "filename": "Assessment.php", "line": "?"}}, "App\\Models\\Course": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Semester": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}}, "count": 167, "is_counter": true}, "livewire": {"data": {"app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores #HsXZ1zvo1TkEs2fCRnZd": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"score_filter\" => array:5 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n        \"course_id\" => \"326\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => []\n    \"defaultActionArguments\" => []\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"score_filter\" => array:5 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n        \"course_id\" => \"326\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores\"\n  \"component\" => \"App\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores\"\n  \"id\" => \"HsXZ1zvo1TkEs2fCRnZd\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores@mountAction<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:159-212</a>", "middleware": "web", "duration": "1.88s", "peak_memory": "14MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-968320555 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-968320555\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1495175338 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kC06G50dbGZ2IV1FAwiSeaQX9tJD12xxEVKNd5kn</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1952 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;score_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;,&quot;course_id&quot;:&quot;326&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:true,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;score_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;,&quot;course_id&quot;:&quot;326&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;HsXZ1zvo1TkEs2fCRnZd&quot;,&quot;name&quot;:&quot;app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores&quot;,&quot;path&quot;:&quot;staff\\/scores\\/scores&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;0f286b4979477cc54d0154c89cd10e7b22729ea1a077f1cde1c64683be7f7f14&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">download_FilledScoresheet</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495175338\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1772617861 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkJqd2dSSjRkazZQbTF3NjJISW9aTHc9PSIsInZhbHVlIjoiZkJJS1MzeTRxWWxxMFVDbDNPQTZUSkIrZUxVYmZHR0F0NnNKYWlkQ0V6d2ZOaW9QRmo4eHp2UVJoY21KdVJGRVd3c2JaTW1vZHUvMWxyZUFqNmsvWFNZYU04d0ZKWEg2aHZZQytxNFlmV0xRMldjODVtVjl6bjRNSnpPMzlmaWYxT08zMHNIM1U4NGNvSkcrTmJYcGJKOXhxOTNDUXRDM0FGQkgwNGJHYmRmb2tuKzJYdnBoUEVaOEU2SG9UdG9ycE5xc2RSdFdKTDMrWUQrNGM5em1BQkdiT3BCY2xqeDVMVldGd2xQemhYUT0iLCJtYWMiOiI0NzM3Y2NiM2RhZGE2YTQ2MmJhNjAyY2UyZmNhZmRjOGZiZTRhMzg4ZjJkN2M2ODQzZWM4NjdkOTIyNzgwODQyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IldYUFRySXJCbTBtaXBjUFJmU1l5TlE9PSIsInZhbHVlIjoiZGNGYU1OYm1KOU9PL3dLaEJua0JlNW1kQkxYeng4Uzh0Z0tPZHQxeFFvL1BtVkVic2dybnJBZ0hFVitOVXFHU00rSTgwaytZanBVcXA3c3ZDYjN2TlJsLzBGTzVERzJYOWNCRzNtQlRmNFV0MU12cVdLOE5MejVtVEZOUzJJSjUiLCJtYWMiOiJjZjhmNGFkNDJkNzhmNjUzY2EzMTIwMTM2MzI5OThhNTEwOGI4YmE3MjMxMzcwMmVkYTRlZjAxOTVjYzM4NjEzIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IjVIaEpCOExqMTJISk9ETUxhNWVKL3c9PSIsInZhbHVlIjoiSlpwTXZveGc1eFc5OUFWTnRyRXlyWCtWOHB6UEdhdWV2ejd6U0xlSDNvZFlkcjZqaW1MTFFtUEgvRTNHNUx6REErdWJaK2wvclo3RUIraWhxMXhwelNoYmJVMWZSMkZHTVZ5ZXB1U0Jqd2FsajZlSVBpbkM4UDBEWlpkaldyVzQiLCJtYWMiOiIxODNiZDMwM2I5ZjBjNGIwM2QzMGVjZDIxZjgwMjQyMTBhOTQyNzU4M2FlMmY1MTAzOGZiMjc5YzRlMTI4ZTBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"262 characters\">https://portal.racoed.test/staff/scores/scores?tableFilters[score_filter][school_session_id]=3&amp;tableFilters[score_filter][semester_id]=1&amp;tableFilters[score_filter][level_id]=1&amp;tableFilters[score_filter][department_id]=16&amp;tableFilters[score_filter][course_id]=326</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2391</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772617861\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1472017230 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|hwcJ4OuuwQL0PF8lFxcDDwdyvAE0tEze6pcPYby2S5UPWNUUuLsx0ygIxRDO|$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kC06G50dbGZ2IV1FAwiSeaQX9tJD12xxEVKNd5kn</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tNqsA2mAWGTQPjNcsrbHj7I5nIy2ikyq9DNmXps1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472017230\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1854732677 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 14 Sep 2025 20:34:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854732677\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-394157798 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kC06G50dbGZ2IV1FAwiSeaQX9tJD12xxEVKNd5kn</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageScores_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>score_filter</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n        \"<span class=sf-dump-key>course_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">326</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>ListStudents_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>session_semester_level_programme_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>programme_id</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"130 characters\">https://portal.racoed.test/application-data/download/30?signature=165f6205278d4885d2b5ff9fdb5525aadaa3be9873a44c2445adef4b99aec4e2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394157798\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}