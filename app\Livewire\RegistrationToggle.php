<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Registration;
use Livewire\Attributes\On;

class RegistrationToggle extends Component
{
    public $registration;
    public $isActive;
    public $userId;

    public function mount(Registration $registration)
    {
        $this->registration = $registration;
        $this->isActive = $registration->is_active;
        $this->userId = $registration->user_id;
    }

    public function toggle()
    {
        $newStatus = !$this->isActive;

        if ($newStatus) {
            $this->registration->user->registrations()->update(['is_active' => false]);
            // Notify other toggles for this user to turn off
            $this->dispatch('registration-toggled', userId: $this->userId, activeRegistrationId: $this->registration->id);
        }

        $this->registration->update(['is_active' => $newStatus]);
        $this->isActive = $newStatus;
    }

    #[On('registration-toggled')]
    public function onRegistrationToggled($userId, $activeRegistrationId)
    {
        // Only respond if it's for the same user and not this registration
        if ($this->userId == $userId && $this->registration->id != $activeRegistrationId) {
            $this->isActive = false;
        }
    }
    public function render()
    {
        return view('livewire.registration-toggle');
    }
}
