<?php

namespace App\Filament\Staff\Clusters\Scores\Resources;

use App\Enums\Role;
use App\Models\User;
use App\Models\Grade;
use App\Models\Level;
use App\Models\Score;
use App\Models\Course;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Semester;
use App\Models\Assessment;
use App\Models\Department;
use App\Models\Scoresheet;
use App\Models\TotalScore;
use Filament\Tables\Table;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\Indicator;
use App\Filament\Staff\Clusters\Scores;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Pages\SubNavigationPosition;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextInputColumn;
use App\Filament\Staff\Clusters\Scores\Resources\ScoreResource\Pages;
use App\Filament\Staff\Clusters\Scores\Resources\ScoreResource\RelationManagers;


class ScoreResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $cluster = Scores::class;
    protected static ?int $navigationSort = 1;
    protected static ?string $modelLabel = 'score';
    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function canAccess(): bool
    {
        return main_staff_access() || Auth::user()?->role === Role::HOD;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('role', Role::STUDENT)
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED));
    }

    public static function table(Table $table): Table
    {
        $grades = Grade::get();

        $failedScore = Grade::where('min_score', 0)
            ->value('max_score');

        return $table
            ->deferLoading()
            ->deferFilters()
            ->persistFiltersInSession()
            ->paginated([10, 20, 30, 40, 50])
            ->striped()
            ->recordAction(null)
            ->defaultSort('last_name')
            ->emptyStateHeading(fn($livewire) => new HtmlString(self::getEmptyStateHeading($livewire)))
            ->emptyStateDescription(fn($livewire) => new HtmlString(self::getEmptyStateDescription($livewire)))
            ->description(new HtmlString('
                    Scores allows lecturers to input assessment Scores for their respective courses, ensuring accurate tracking of student performance.<br>  
                    <b>NOTE:</b>  
                    <ol style="padding-left: 20px; list-style-type: decimal; font-size: 0.875rem;">  
                        <li>Only your permitted departments and courses are displayed.</li>  
                        <li>Enter the score for each assessment.</li>  
                        <li><b>Press Enter</b> or <b>click outside the input</b> to automatically save the scores.</li> 
                       
                    </ol>
                '))
            ->searchPlaceholder('Search (student name)')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->sortable(
                        query: fn($query, $direction) =>
                        $query->orderBy("users.last_name", $direction)
                    )
                    ->searchable(['last_name', 'first_name', 'middle_name']),
                TextColumn::make('matric_number')
                    ->label('Matric no.'),
                ...self::getAssessmentColumns(),
                TextInputColumn::make('total')
                    ->label(new HtmlString("Total<br><span style='text-align: left; display: block;'>(100)</span>"))
                    ->extraAttributes([
                        'style' => 'width: 80px !important; min-width: 80px !important; max-width: 80px !important;'
                    ])
                    ->extraInputAttributes([
                        'min' => 0,
                        'tabindex' => '-1',
                    ])
                    ->rules([
                        'numeric',
                        'min:0',
                        'max:100',
                    ])
                    ->validationAttribute('Total')
                    ->state(function ($record, $livewire) {
                        $filters = self::extractFilters($livewire);
                        $totalScore = self::getTotalScore($record, $filters);
                        $record->totalScore = $totalScore;

                        return $totalScore ? (string) $totalScore : null;
                    })
                    ->updateStateUsing(fn($record, $state, $livewire) => self::updateTotalScore($record, $state, $livewire)),
                TextColumn::make('grade')
                    ->badge()
                    ->color(fn($record): string => ($record->totalScore <= $failedScore) ? 'danger' : 'gray')
                    ->state(function ($record) use ($grades) {
                        if (!isset($record->totalScore)) return null;

                        $grade = self::getGrade($grades, $record->totalScore);
                        $record->gradeObject = $grade;

                        return $grade?->name;
                    }),

                TextColumn::make('remark')
                    ->badge()
                    ->color(fn($record): string => ($record->totalScore <= $failedScore) ? 'danger' : 'gray')
                    ->state(fn($record) => $record->gradeObject?->remark),
            ])

            ->filters([
                SelectFilter::make('score_filter')
                    ->form([
                        Select::make('school_session_id')
                            ->required()
                            ->label('Session')
                            ->native(false)
                            ->options(function () {
                                return SchoolSession::query()
                                    ->orderBy('name', 'desc')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSchoolSession()->id),
                        Select::make('semester_id')
                            ->required()
                            ->label('Semester')
                            ->native(false)
                            ->live()
                            ->options(function () {
                                return Semester::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSemester()?->id)
                            ->afterStateUpdated(fn(Set $set) => $set('course_id', null)),
                        Select::make('level_id')
                            ->required()
                            ->label('Level')
                            ->live()
                            ->native(false)
                            ->options(function () {
                                return Level::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->afterStateUpdated(fn(Set $set) => $set('course_id', null)),
                        Select::make('department_id')
                            ->required()
                            ->label('Department')
                            ->native(false)
                            ->searchable()
                            ->live()
                            ->options(function () {
                                $query = Department::query()->orderBy('name');

                                if (Auth::user()->role === Role::HOD) {
                                    $query->where('id', Auth::user()->department_id);
                                }

                                return $query->pluck('name', 'id');
                            })
                            ->afterStateUpdated(fn(Set $set) => $set('course_id', null)),
                        Select::make('course_id')
                            ->required()
                            ->label('Course')
                            // ->native(false)
                            ->live()
                            ->placeholder(
                                fn(Get $get) =>
                                empty($get('department_id')) || empty($get('semester_id')) || empty($get('level_id'))
                                    ? 'Select all options first'
                                    : 'Select an option'
                            )
                            ->options(function (Get $get) {
                                if (empty($get('semester_id')) || empty($get('department_id'))  || empty($get('level_id'))) {
                                    return [];
                                }
                                return Course::query()
                                    ->where('department_id', $get('department_id'))
                                    ->where('semester_id', $get('semester_id'))
                                    ->where('level_id', $get('level_id'))
                                    ->orderBy('code')
                                    ->pluck('code', 'id');
                            })
                            ->afterStateUpdated(function (Get $get, Set $set) {
                                if (empty($get('course_id'))) {
                                    $set('course_id', null);
                                }
                            }),
                    ])
                    ->columns(4)
                    ->baseQuery(function (Builder $query, HasTable $livewire): Builder {
                        if (!self::hasRequiredFilters($livewire) || !self::isScoresheetCreated($livewire)) {
                            return $query->whereRaw('1 = 0');
                        }

                        if (!self::isScoresheetCreated($livewire)) {
                            return $query->whereRaw('1 = 0');
                        }

                        return $query;
                    })
                    ->query(function (Builder $query, array $data) {
                        $department = Department::find($data['department_id']);

                        return $query->whereHas('registrations', function ($q) use ($data, $department) {
                            $q->where('school_session_id', $data['school_session_id'])
                                ->where('semester_id', $data['semester_id'])
                                ->where('level_id', $data['level_id']);

                            if (!($department?->is_edu || $department?->is_gse)) {
                                $q->where(function ($q) use ($data) {
                                    $q->whereHas('programme', function ($q) use ($data) {
                                        $q->where('first_department_id', $data['department_id'])
                                            ->orWhere('second_department_id', $data['department_id']);
                                    });
                                });
                            }
                        });
                    })

                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($sessionId = $data['school_session_id'] ?? null) {
                            if ($name = SchoolSession::find($sessionId)?->name) {
                                $indicators[] = Indicator::make("Session: {$name}")->removable(false);
                            }
                        }

                        if ($semesterId = $data['semester_id'] ?? null) {
                            if ($name = Semester::find($semesterId)?->name) {
                                $indicators[] = Indicator::make("Semester: {$name}")->removable(false);
                            }
                        }

                        if ($levelId = $data['level_id'] ?? null) {
                            if ($name = Level::find($levelId)?->name) {
                                $indicators[] = Indicator::make("Level: {$name}")->removable(false);
                            }
                        }

                        if ($courseId = $data['course_id'] ?? null) {
                            if ($course = Course::find($courseId)) {
                                $code = $course->code;
                                $credit = $course->credit;
                                $status = $course->course_status->value;

                                $indicators[] = Indicator::make("Course: {$code} ({$credit}{$status})")
                                    ->removable(false);
                            }
                        }

                        return $indicators;
                    })

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(1)
            ->filtersApplyAction(
                fn(Action $action) => $action->label('View scores'),
            );
    }

    private static function getAssessmentColumns(): array
    {
        return Assessment::select('id', 'name', 'max_score')
            ->get()
            ->map(fn($assessment) => self::createAssessmentColumn($assessment))
            ->toArray();
    }

    private static function createAssessmentColumn($assessment): TextInputColumn
    {
        return TextInputColumn::make("Scores.{$assessment->id}")
            ->disabled(fn($livewire) => self::isScoresheetPublished($livewire) && ! main_staff_access())
            ->rules([
                'numeric',
                'min:0',
                "max:{$assessment->max_score}",
            ])
            ->extraAttributes([
                'style' => 'width: 80px !important; min-width: 80px !important; max-width: 80px !important;'
            ])
            ->extraInputAttributes(['min' => 0])
            ->label(new HtmlString(
                "{$assessment->name}<br><span style='text-align: left; display: block;'>({$assessment->max_score})</span>"
            ))
            ->validationAttribute("{$assessment->name} ({$assessment->max_score})")
            ->state(fn($record, $livewire) => self::getScoreForAssessment($record, $assessment, $livewire))
            ->updateStateUsing(fn($record, $state, $livewire) => self::updateScoreForAssessment($record, $state, $assessment, $livewire));
    }

    private static function getScoreForAssessment($record, $assessment, $livewire): ?string
    {
        $filters = self::extractFilters($livewire);

        $registration = self::getRegistration($record->id, $filters);

        if (!$registration) {
            return null;
        }

        return Score::where([
            'registration_id' => $registration->id,
            'course_id' => $filters['course_id'],
            'assessment_id' => $assessment->id,
        ])->value('score');
    }

    private static function updateScoreForAssessment($record, $state, $assessment, $livewire): void
    {
        $filters = self::extractFilters($livewire);

        if (!self::hasRequiredFilters($livewire)) {
            self::sendScoreFailedNotification();
            return;
        }

        $registration = self::getRegistration($record->id, $filters);

        if (!$registration) {
            self::sendScoreFailedNotification();
            return;
        }

        DB::transaction(function () use ($registration, $filters, $assessment, $state) {
            self::updateOrCreateScore($registration->id, $filters['course_id'], $assessment->id, $state);
            self::updateOrCreateTotalScore($registration->id, $filters['course_id']);
        });
    }

    private static function updateOrCreateScore(int $registrationId, int $courseId, int $assessmentId, $score): void
    {
        Score::updateOrCreate(
            [
                'registration_id' => $registrationId,
                'course_id' => $courseId,
                'assessment_id' => $assessmentId,
            ],
            ['score' => $score]
        );
    }

    private static function updateOrCreateTotalScore(int $registrationId, int $courseId): void
    {
        $conditions = [
            'registration_id' => $registrationId,
            'course_id' => $courseId,
        ];

        $hasAnyScore = Score::where($conditions)
            ->whereNotNull('score')
            ->exists();

        if ($hasAnyScore) {
            $totalScore = Score::where($conditions)->sum('score');
            TotalScore::updateOrCreate($conditions, ['total' => $totalScore]);
        } else {
            TotalScore::where($conditions)->delete();
        }
    }

    public static function getTotalScore($record, array $filters): ?int
    {
        $registration = self::getRegistration($record->id, $filters);

        if (!$registration) {
            return null;
        }

        return TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $filters['course_id'],
        ])->value('total');
    }

    private static function updateTotalScore($record, $state, $livewire): void
    {
        $filters = self::extractFilters($livewire);

        if (!self::hasRequiredFilters($livewire)) {
            self::sendScoreFailedNotification();
            return;
        }

        $registration = self::getRegistration($record->id, $filters);

        if (!$registration) {
            self::sendScoreFailedNotification();
            return;
        }

        DB::transaction(function () use ($registration, $filters, $state) {
            TotalScore::updateOrCreate(
                [
                    'registration_id' => $registration->id,
                    'course_id' => $filters['course_id'],
                ],
                ['total' => $state]
            );

            Score::where([
                'registration_id' => $registration->id,
                'course_id' => $filters['course_id'],
            ])->delete();
        });
    }

    public static function getRegistration(int $userId, array $filters): ?Registration
    {
        return Registration::where([
            'user_id' => $userId,
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'level_id' => $filters['level_id'],
        ])->first();
    }

    public static function getGrade($grades, ?int $totalScore)
    {
        if (!$totalScore) return null;

        return $grades->first(
            fn($grade) =>
            $totalScore >= $grade->min_score && $totalScore <= $grade->max_score
        );
    }

    public static function isScoresheetCreated(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Scoresheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
        ])->exists();
    }

    public static function isScoresheetPublished(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Scoresheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
            'is_published' => true,
        ])->exists();
    }

    private static function getEmptyStateHeading($livewire): string
    {
        $filters = self::extractFilters($livewire);

        if (!isset($filters['school_session_id'], $filters['semester_id'], $filters['level_id'], $filters['course_id'])) {
            return 'All Options Must Be Selected to Add Scores';
        }

        if (!self::isScoresheetCreated($livewire)) {
            return 'Scoresheet Has Not Been Created Yet';
        }

        return 'No Students Found';
    }

    private static function getEmptyStateDescription($livewire): string
    {
        $filters = self::extractFilters($livewire);

        if (!isset($filters['school_session_id'], $filters['semester_id'], $filters['level_id'], $filters['course_id'])) {
            return 'Select <b>session</b>, <b>semester</b>, <b>level</b>, <b>department</b>, and <b>course</b> to add scores.';
        }

        if (!self::isScoresheetCreated($livewire)) {
            return 'The scoresheet for this session, semester, and department has not been created yet. <b>Contact your admin</b> for more information.';
        }

        return 'No students found for the selected options.';
    }

    private static function extractFilters(HasTable $livewire): array
    {
        $filters = $livewire->tableFilters['score_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
            'course_id' => $filters['course_id'] ?? null,
        ];
    }

    public static function hasRequiredFilters(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return isset(
            $filters['school_session_id'],
            $filters['semester_id'],
            $filters['level_id'],
            $filters['course_id']
        );
    }

    private static function sendScoreFailedNotification(): void
    {
        Notification::make()
            ->danger()
            ->title('Scoreing Failed')
            ->body('Some required filters are missing.')
            ->send();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageScores::route('/'),
        ];
    }
}
