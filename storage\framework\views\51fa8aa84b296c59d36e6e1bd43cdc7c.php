<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="<?php echo e(asset('images/racoed-favicon.png')); ?>" type="image/png">   
    <title>Application Data - <?php echo e($student->name); ?></title>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo \Filament\Support\Facades\FilamentAsset::renderStyles() ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/filament/custom/theme.css', 'resources/css/app.css']); ?>
    <style>
           @media print {
            @page { size: A4; margin: 0mm; }
            .print\:hidden { display: none !important; }
            body { font-size: 10px; }
            h1 { font-size: 16px !important; }
            th, td { font-size: 10px !important; }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        
        <?php if (isset($component)) { $__componentOriginal748b58a65cd16e6aeabed19711d43de4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal748b58a65cd16e6aeabed19711d43de4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.document-header','data' => ['collegeLogo' => asset('images/racoed-favicon.png'),'collegeName' => $collegeSettings->name,'collegeMotto' => $collegeSettings->motto,'collegeAddress' => $collegeSettings->address,'collegePhone' => $collegeSettings->phone,'collegeEmail' => $collegeSettings->email,'studentPhoto' => $student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('document-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['collegeLogo' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(asset('images/racoed-favicon.png')),'collegeName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->name),'collegeMotto' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->motto),'collegeAddress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->address),'collegePhone' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->phone),'collegeEmail' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->email),'studentPhoto' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($student->photo ? Storage::url($student->photo) : asset('images/placeholder.png'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal748b58a65cd16e6aeabed19711d43de4)): ?>
<?php $attributes = $__attributesOriginal748b58a65cd16e6aeabed19711d43de4; ?>
<?php unset($__attributesOriginal748b58a65cd16e6aeabed19711d43de4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal748b58a65cd16e6aeabed19711d43de4)): ?>
<?php $component = $__componentOriginal748b58a65cd16e6aeabed19711d43de4; ?>
<?php unset($__componentOriginal748b58a65cd16e6aeabed19711d43de4); ?>
<?php endif; ?> 

        
        <div class="border rounded p-2">
            <h2 class="text-center font-bold mb-2">Applicant Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3">
                <div class="border p-2"><strong>Name:</strong> <?php echo e($student->name ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Date of birth:</strong> <?php echo e($student->date_of_birth?->format('d M, Y') ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Gender:</strong> <?php echo e($student->gender ?? 'NIL'); ?></div>                    
                <div class="border p-2"><strong>Marital status:</strong> <?php echo e($student->marital_status ?? 'NIL'); ?></div>  
                <div class="border p-2"><strong>Religion:</strong> <?php echo e($student->religion ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Nationality:</strong> <?php echo e($student->nationality ?? 'NIL'); ?></div>     
                <div class="border p-2"><strong>State of origin:</strong> <?php echo e($student->state->name ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>LGA:</strong> <?php echo e($student->localGovernmentArea->name ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Phone:</strong> <?php echo e($student->phone ?? 'NIL'); ?></div>  
                <div class="border p-2"><strong>Email:</strong> <?php echo e($student->email ?? 'NIL'); ?></div> 
                <div class="border p-2 sm:col-span-2"><strong>Address:</strong> <?php echo e($student->address ?? 'NIL'); ?></div>                
            </div>
            </div>
        </div>
        
        
        <div class="border rounded p-2">
            <h2 class="text-center font-bold mb-2">Guardian Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3">
                <div class="border p-2"><strong>Guardian name:</strong><?php echo e($student->guardian ? ($student->guardian->title?->getLabel() . ' ' . $student->guardian->name) : 'NIL'); ?></div>        
                <div class="border p-2"><strong>Relationship:</strong> <?php echo e($student->guardian?->relationship?->getLabel() ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Occupation:</strong> <?php echo e($student->guardian?->occupation?->getLabel() ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Phone:</strong> <?php echo e($student->guardian?->phone ?? 'NIL'); ?></div>
            </div>
        </div>

        
        <div class="border rounded p-2">
            <h2 class="text-center font-bold mb-2">Application Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3">
                <div class="border p-2"><strong>Application date:</strong> <?php echo e($student->application?->created_at?->format('d M, Y') ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Application number:</strong> <?php echo e($student->application?->number ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>JAMB reg. no.:</strong> <?php echo e($student->application?->jamb_registration_number ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Secondary school:</strong> <?php echo e($student->application?->secondary_school_attended ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Graduation year:</strong> <?php echo e($student->application?->secondary_school_graduation_year ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Exam board:</strong> <?php echo e($student->application?->exam_board ?? 'NIL'); ?></div>                
                <div class="border p-2"><strong>Entry session:</strong> <?php echo e($student->application?->schoolSession->name ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Entry programme:</strong> <?php echo e($student->application?->programme->name ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Screening score:</strong> <?php echo e($student->application?->screening_score ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Screening status:</strong> <?php echo e($student->application?->screening_status->getLabel() ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Admission status:</strong> <?php echo e($student->application?->admission_status->getLabel() ?? 'NIL'); ?></div>
                <div class="border p-2"><strong>Admission date:</strong> <?php echo e($student->application?->admission_date?->format('d M, Y') ?? 'NIL'); ?></div>
            </div>

            
            <?php
            $results = $student->application?->exam_result ?? [];
            $chunks = count($results) > 0 ? array_chunk($results, ceil(count($results) / 3), true) : [];
            ?> 

            <div class="mt-4">
                <h3 class="font-bold mb-2">Exam Results:</h3>
                <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3 gap-4 text-sm">
                    <?php $__currentLoopData = $chunks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <table class="w-full border border-collapse">
                            <thead>
                                <tr>
                                    <th class="border px-2 py-1 text-left">Subject</th>
                                    <th class="border px-2 py-1 text-left">Grade</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="border px-2 py-1"><?php echo e(\App\Enums\OLevelSubject::tryFrom($item['subject'])?->getLabel()); ?></td>
                                    <td class="border px-2 py-1"><?php echo e(strtoupper($item['grade'])); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

        </div>

        
        <div class="fixed bottom-4 right-4 print:hidden">
            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']); ?>
                Print application data
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
        </div>

        <script>
            // Automatically open print dialog when the page loads
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/documents/application-data.blade.php ENDPATH**/ ?>