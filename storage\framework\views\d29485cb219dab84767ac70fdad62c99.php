<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="<?php echo e(asset('images/racoed-favicon.png')); ?>" type="image/png">
    <title><?php echo e($fileName); ?></title>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <?php echo \Filament\Support\Facades\FilamentAsset::renderStyles() ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/filament/custom/theme.css', 'resources/css/app.css']); ?>
    <style>
           @media print {
            @page { size: A4; margin: 0mm; }
             .college-name-print { font-size: 24pt !important; }
            .print\:hidden { display: none !important; }
            body { font-size: 10px; }
            h1 { font-size: 16px !important; }
            th, td { font-size: 10px !important; }
            .print-grid {
                    display: grid !important;
                    grid-template-columns: repeat(3, 1fr) !important;
                }
                th, tfoot tr {
                  background-color: #f9fafb !important; /* Tailwind's gray-50 */
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
              }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        
        <?php if (isset($component)) { $__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.document-simple-header','data' => ['collegeLogo' => asset('images/racoed-favicon.png'),'collegeName' => $collegeSettings->name,'heading' => 'Examination & Records Office','subheading' => 'Result Scoresheet']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('document-simple-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['collegeLogo' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(asset('images/racoed-favicon.png')),'collegeName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($collegeSettings->name),'heading' => 'Examination & Records Office','subheading' => 'Result Scoresheet']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a)): ?>
<?php $attributes = $__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a; ?>
<?php unset($__attributesOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a)): ?>
<?php $component = $__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a; ?>
<?php unset($__componentOriginal6f15e3d1e6eadd59d2c0c4cdc59d0e3a); ?>
<?php endif; ?> 

        
        <div class="border  p-1">
            <h2 class="text-center font-bold mb-2">Course Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1 print-grid">
                <div class="border p-1"><strong>Session:</strong> <?php echo e($session ?? 'NIL'); ?></div>
                <div class="border p-1"><strong>Semester:</strong> <?php echo e($semester ?? 'NIL'); ?></div>   
                <div class="border p-1"><strong>Level:</strong> <?php echo e($level ?? 'NIL'); ?></div>  
                <div class="border p-1"><strong>Department:</strong> <?php echo e($department ?? 'NIL'); ?></div>  
                <div class="border p-1 sm:col-span-2">
                        <strong>Course:</strong>
                        <?php if($course): ?>
                            <?php echo e($course->code); ?>

                            <?php echo e($course->credit ? ' | ' . $course->credit : ''); ?>

                            <?php echo e($course->course_status ? $course->course_status->getAlias() : ''); ?>

                            <?php echo e($course->title ? ' | ' . $course->title : ''); ?>

                        <?php else: ?>
                            NIL
                        <?php endif; ?>
                    </div>                 
            </div>
        </div>

        
        <div class="border  p-1">
            <h2 class="text-center font-bold mb-2">Course Scores</h2>  
            
            <table class="w-full border border-gray-300 text-xs">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-2 py-1 text-center">#</th>
                        <th class="border px-2 py-1 text-left">Name</th>
                        <th class="border px-2 py-1 text-left">Matric. no.</th>
                        <?php $__currentLoopData = $assessments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assessment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <th class="border px-2 py-1 text-center">
                                <?php echo e($assessment->name); ?><br>
                                <span class="block text-xs">(<?php echo e($assessment->max_score); ?>)</span>
                            </th>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <th class="border px-2 py-1 text-center">Total<br><span class="block text-xs">(<?php echo e($assessments->sum('max_score')); ?>)</span></th>
                        <th class="border px-2 py-1 text-center">Grade</th>
                    </tr>
                </thead>
                 <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="border px-2 py-1 text-center"><?php echo e($index + 1); ?></td>
                            <td class="border px-2 py-1 text-left"><?php echo e($student['name']); ?></td>
                            <td class="border px-2 py-1 text-left"><?php echo e($student['matric_number']); ?></td>

                            <?php $__currentLoopData = $assessments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assessment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="border px-2 py-1 text-center">
                                    <?php if($blank): ?>
                                        
                                    <?php else: ?>
                                        <?php echo e($student['assessmentScores'][$assessment->name] ?? '-'); ?>

                                    <?php endif; ?>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <td class="border px-2 py-1 text-center">
                                <?php if($blank): ?>
                                    
                                <?php else: ?>
                                    <?php echo e($student['totalScore'] ?? '-'); ?>

                                <?php endif; ?>
                            </td>

                            <td class="border px-2 py-1 text-center">
                                <?php if($blank): ?>
                                    
                                <?php else: ?>
                                    <?php echo e($student['grade'] ?? '-'); ?>

                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="<?php echo e(4 + count($assessments)); ?>" class="border px-2 py-2 text-center">No students found</td>
                        </tr>
                    <?php endif; ?>
                </tbody>

            </table>
        </div>

      
        <div class="mt-6">
            <p class="font-bold">Lecturer in Charge</p>
            <p><span class="font-bold">Name:</span> ____________________________________</p>
            <p><span class="font-bold">Sign & Date:</span> _____________________________</p>
        </div>

      
        

        
        <div class="fixed bottom-4 right-4 print:hidden">
            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tag' => 'button','color' => 'primary','icon' => 'heroicon-o-printer','onclick' => 'window.print()']); ?>
                Print scoresheet
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
        </div>

        <script>
            // Automatically open print dialog when the page loads
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>

<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/documents/scoresheet.blade.php ENDPATH**/ ?>