{"__meta": {"id": "01K54SJCDX5DMFNBNPB7MDM6Z7", "datetime": "2025-09-14 19:00:00", "utime": **********.573894, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.333299, "end": **********.573921, "duration": 1.2406220436096191, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": **********.333299, "relative_start": 0, "end": **********.678266, "relative_end": **********.678266, "duration": 0.****************, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.678287, "relative_start": 0.****************, "end": **********.573925, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "896ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.687495, "relative_start": 0.*****************, "end": **********.688607, "relative_end": **********.688607, "duration": 0.0011119842529296875, "duration_str": "1.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.hooks.scoresheet-banner", "start": **********.935859, "relative_start": 0.****************, "end": **********.935859, "relative_end": **********.935859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0e562057e53b176eb249937b70a802f8", "start": **********.971445, "relative_start": 0.***************, "end": **********.971445, "relative_end": **********.971445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.096122, "relative_start": 0.7628231048583984, "end": **********.096122, "relative_end": **********.096122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.1152, "relative_start": 0.7819011211395264, "end": **********.1152, "relative_end": **********.1152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.127463, "relative_start": 0.7941641807556152, "end": **********.127463, "relative_end": **********.127463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.140478, "relative_start": 0.8071789741516113, "end": **********.140478, "relative_end": **********.140478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.156649, "relative_start": 0.823350191116333, "end": **********.156649, "relative_end": **********.156649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.163403, "relative_start": 0.8301041126251221, "end": **********.163403, "relative_end": **********.163403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.448835, "relative_start": 1.1155359745025635, "end": **********.448835, "relative_end": **********.448835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.455354, "relative_start": 1.1220550537109375, "end": **********.455354, "relative_end": **********.455354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3e7af8a9f04a41a7583e36fe677e9b1b", "start": **********.457258, "relative_start": 1.1239590644836426, "end": **********.457258, "relative_end": **********.457258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.464084, "relative_start": 1.1307849884033203, "end": **********.464084, "relative_end": **********.464084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ba9cccfb20917de418a9fa74743df081", "start": **********.470602, "relative_start": 1.137303113937378, "end": **********.470602, "relative_end": **********.470602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.494328, "relative_start": 1.1610291004180908, "end": **********.494328, "relative_end": **********.494328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.513372, "relative_start": 1.1800730228424072, "end": **********.513372, "relative_end": **********.513372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ba9cccfb20917de418a9fa74743df081", "start": **********.516548, "relative_start": 1.1832489967346191, "end": **********.516548, "relative_end": **********.516548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.536426, "relative_start": 1.203127145767212, "end": **********.536426, "relative_end": **********.536426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::660f48158600ad3cb47085b84e3ebc79", "start": **********.559742, "relative_start": 1.2264430522918701, "end": **********.559742, "relative_end": **********.559742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.570389, "relative_start": 1.2370901107788086, "end": **********.573328, "relative_end": **********.573328, "duration": 0.002938985824584961, "duration_str": "2.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7790944, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 18, "nb_templates": 18, "templates": [{"name": "filament.hooks.scoresheet-banner", "param_count": null, "params": [], "start": **********.935589, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.phpfilament.hooks.scoresheet-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fscoresheet-banner.blade.php&line=1", "ajax": false, "filename": "scoresheet-banner.blade.php", "line": "?"}}, {"name": "__components::0e562057e53b176eb249937b70a802f8", "param_count": null, "params": [], "start": **********.97118, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/0e562057e53b176eb249937b70a802f8.blade.php__components::0e562057e53b176eb249937b70a802f8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F0e562057e53b176eb249937b70a802f8.blade.php&line=1", "ajax": false, "filename": "0e562057e53b176eb249937b70a802f8.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.096023, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.115093, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.127381, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.140364, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.156571, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.163297, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.448755, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.455281, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::3e7af8a9f04a41a7583e36fe677e9b1b", "param_count": null, "params": [], "start": **********.457185, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/3e7af8a9f04a41a7583e36fe677e9b1b.blade.php__components::3e7af8a9f04a41a7583e36fe677e9b1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F3e7af8a9f04a41a7583e36fe677e9b1b.blade.php&line=1", "ajax": false, "filename": "3e7af8a9f04a41a7583e36fe677e9b1b.blade.php", "line": "?"}}, {"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.464007, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::ba9cccfb20917de418a9fa74743df081", "param_count": null, "params": [], "start": **********.470418, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/ba9cccfb20917de418a9fa74743df081.blade.php__components::ba9cccfb20917de418a9fa74743df081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fba9cccfb20917de418a9fa74743df081.blade.php&line=1", "ajax": false, "filename": "ba9cccfb20917de418a9fa74743df081.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.494276, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.513319, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::ba9cccfb20917de418a9fa74743df081", "param_count": null, "params": [], "start": **********.516446, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/ba9cccfb20917de418a9fa74743df081.blade.php__components::ba9cccfb20917de418a9fa74743df081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Fba9cccfb20917de418a9fa74743df081.blade.php&line=1", "ajax": false, "filename": "ba9cccfb20917de418a9fa74743df081.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.536344, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}, {"name": "__components::660f48158600ad3cb47085b84e3ebc79", "param_count": null, "params": [], "start": **********.559658, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/660f48158600ad3cb47085b84e3ebc79.blade.php__components::660f48158600ad3cb47085b84e3ebc79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F660f48158600ad3cb47085b84e3ebc79.blade.php&line=1", "ajax": false, "filename": "660f48158600ad3cb47085b84e3ebc79.blade.php", "line": "?"}}]}, "queries": {"count": 90, "nb_statements": 85, "nb_visible_statements": 90, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.15599999999999997, "accumulated_duration_str": "156ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.694137, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'tNqsA2mAWGTQPjNcsrbHj7I5nIy2ikyq9DNmXps1' limit 1", "type": "query", "params": [], "bindings": ["tNqsA2mAWGTQPjNcsrbHj7I5nIy2ikyq9DNmXps1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.6952171, "duration": 0.019989999999999997, "duration_str": "19.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 12.814}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.7241862, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 12.814, "width_percent": 0.628}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 64}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.7373621, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:64", "source": {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=64", "ajax": false, "filename": "ScoreResource.php", "line": "64"}, "connection": "racoed", "explain": null, "start_percent": 13.442, "width_percent": 0.865}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.7419338, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:67", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=67", "ajax": false, "filename": "ScoreResource.php", "line": "67"}, "connection": "racoed", "explain": null, "start_percent": 14.308, "width_percent": 0.5}, {"sql": "select `id`, `name`, `max_score` from `assessments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 300}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}], "start": **********.746418, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:300", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 300}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=300", "ajax": false, "filename": "ScoreResource.php", "line": "300"}, "connection": "racoed", "explain": null, "start_percent": 14.808, "width_percent": 0.378}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.751487, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 15.186, "width_percent": 0.513}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.7557302, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 15.699, "width_percent": 0.417}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-09-14' and date(`semester_end`) >= '2025-09-14' limit 1", "type": "query", "params": [], "bindings": [3, "2025-09-14", "2025-09-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.759824, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 16.115, "width_percent": 0.519}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.767372, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 16.635, "width_percent": 0.583}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.772154, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 17.218, "width_percent": 0.462}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.776667, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 17.679, "width_percent": 0.404}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`id` = '38' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16", "38"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasColumns.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasColumns.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.7817788, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "racoed", "explain": null, "start_percent": 18.083, "width_percent": 1.051}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasColumns.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasColumns.php", "line": 56}], "start": **********.787037, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 19.135, "width_percent": 0.455}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 352}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanUpdateState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanUpdateState.php", "line": 49}], "start": **********.7922068, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 19.59, "width_percent": 0.506}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanUpdateState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanUpdateState.php", "line": 49}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasColumns.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasColumns.php", "line": 68}], "start": **********.799062, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:359", "source": {"index": 10, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=359", "ajax": false, "filename": "ScoreResource.php", "line": "359"}, "connection": "racoed", "explain": null, "start_percent": 20.096, "width_percent": 0}, {"sql": "select * from `scores` where (`registration_id` = 115 and `course_id` = 326 and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [115, 326, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 360}, {"index": 26, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 27, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.799922, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:367", "source": {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=367", "ajax": false, "filename": "ScoreResource.php", "line": "367"}, "connection": "racoed", "explain": null, "start_percent": 20.096, "width_percent": 0.519}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 360}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.806048, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:367", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=367", "ajax": false, "filename": "ScoreResource.php", "line": "367"}, "connection": "racoed", "explain": null, "start_percent": 20.615, "width_percent": 0}, {"sql": "insert into `scores` (`registration_id`, `course_id`, `assessment_id`, `score`, `updated_at`, `created_at`) values (115, 326, 2, '45', '2025-09-14 18:59:59', '2025-09-14 18:59:59')", "type": "query", "params": [], "bindings": [115, 326, 2, "45", "2025-09-14 18:59:59", "2025-09-14 18:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, {"index": 28, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 360}, {"index": 32, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 33, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 34, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.807205, "duration": 0.03456, "duration_str": "34.56ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:367", "source": {"index": 27, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=367", "ajax": false, "filename": "ScoreResource.php", "line": "367"}, "connection": "racoed", "explain": null, "start_percent": 20.615, "width_percent": 22.154}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 360}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.848857, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:367", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 367}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=367", "ajax": false, "filename": "ScoreResource.php", "line": "367"}, "connection": "racoed", "explain": null, "start_percent": 42.769, "width_percent": 0}, {"sql": "select exists(select * from `scores` where (`registration_id` = 115 and `course_id` = 326) and `score` is not null) as `exists`", "type": "query", "params": [], "bindings": [115, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 386}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 361}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.850218, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:386", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=386", "ajax": false, "filename": "ScoreResource.php", "line": "386"}, "connection": "racoed", "explain": null, "start_percent": 42.769, "width_percent": 1.788}, {"sql": "select sum(`score`) as aggregate from `scores` where (`registration_id` = 115 and `course_id` = 326)", "type": "query", "params": [], "bindings": [115, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 389}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 361}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.863124, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:389", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 389}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=389", "ajax": false, "filename": "ScoreResource.php", "line": "389"}, "connection": "racoed", "explain": null, "start_percent": 44.558, "width_percent": 1.949}, {"sql": "select * from `total_scores` where (`registration_id` = 115 and `course_id` = 326) limit 1", "type": "query", "params": [], "bindings": [115, 326], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 390}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 361}, {"index": 26, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 27, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8771808, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:390", "source": {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 390}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=390", "ajax": false, "filename": "ScoreResource.php", "line": "390"}, "connection": "racoed", "explain": null, "start_percent": 46.506, "width_percent": 1.744}, {"sql": "update `total_scores` set `total` = '57', `total_scores`.`updated_at` = '2025-09-14 18:59:59' where `id` = 31", "type": "query", "params": [], "bindings": ["57", "2025-09-14 18:59:59", 31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 390}, {"index": 21, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 361}, {"index": 25, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 26, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.890971, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:390", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 390}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=390", "ajax": false, "filename": "ScoreResource.php", "line": "390"}, "connection": "racoed", "explain": null, "start_percent": 48.25, "width_percent": 2.417}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, {"index": 10, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 323}, {"index": 11, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 12, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanUpdateState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanUpdateState.php", "line": 49}, {"index": 13, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasColumns.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasColumns.php", "line": 68}], "start": **********.919605, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:359", "source": {"index": 9, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=359", "ajax": false, "filename": "ScoreResource.php", "line": "359"}, "connection": "racoed", "explain": null, "start_percent": 50.667, "width_percent": 0}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": "view", "name": "filament.hooks.scoresheet-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.php", "line": 8}, {"index": 14, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9397411, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 50.667, "width_percent": 1.667}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": "view", "name": "filament.hooks.scoresheet-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/scoresheet-banner.blade.php", "line": 10}, {"index": 14, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.953592, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 52.333, "width_percent": 2.128}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '3' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.980956, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:259", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=259", "ajax": false, "filename": "ScoreResource.php", "line": "259"}, "connection": "racoed", "explain": null, "start_percent": 54.462, "width_percent": 1.353}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.9934208, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:265", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=265", "ajax": false, "filename": "ScoreResource.php", "line": "265"}, "connection": "racoed", "explain": null, "start_percent": 55.814, "width_percent": 1.288}, {"sql": "select * from `levels` where `levels`.`id` = '1' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 271}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.0056472, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:271", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 271}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=271", "ajax": false, "filename": "ScoreResource.php", "line": "271"}, "connection": "racoed", "explain": null, "start_percent": 57.103, "width_percent": 1.199}, {"sql": "select * from `courses` where `courses`.`id` = '326' limit 1", "type": "query", "params": [], "bindings": ["326"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 277}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.0232792, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:277", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=277", "ajax": false, "filename": "ScoreResource.php", "line": "277"}, "connection": "racoed", "explain": null, "start_percent": 58.301, "width_percent": 1.647}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.042516, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 59.949, "width_percent": 0.641}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.047661, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 60.59, "width_percent": 0.538}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.052766, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 61.128, "width_percent": 0.538}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.0587502, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "racoed", "explain": null, "start_percent": 61.667, "width_percent": 1.314}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null order by `last_name` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.065294, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 62.981, "width_percent": 1.929}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 154}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.086455, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:154", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 154}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=154", "ajax": false, "filename": "ScoreResource.php", "line": "154"}, "connection": "racoed", "explain": null, "start_percent": 64.91, "width_percent": 0.564}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 165}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.1068761, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:165", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=165", "ajax": false, "filename": "ScoreResource.php", "line": "165"}, "connection": "racoed", "explain": null, "start_percent": 65.474, "width_percent": 0.526}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 177}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.121598, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:177", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=177", "ajax": false, "filename": "ScoreResource.php", "line": "177"}, "connection": "racoed", "explain": null, "start_percent": 66, "width_percent": 0.423}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 193}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.131994, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:193", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=193", "ajax": false, "filename": "ScoreResource.php", "line": "193"}, "connection": "racoed", "explain": null, "start_percent": 66.423, "width_percent": 0.603}, {"sql": "select `code`, `id` from `courses` where `department_id` = '16' and `semester_id` = '1' and `level_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 216}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.148554, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:216", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 216}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=216", "ajax": false, "filename": "ScoreResource.php", "line": "216"}, "connection": "racoed", "explain": null, "start_percent": 67.026, "width_percent": 2.603}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.200035, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 69.628, "width_percent": 0.519}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.204026, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 70.147, "width_percent": 0.66}, {"sql": "select `score` from `scores` where (`registration_id` = 107 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [107, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.209012, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 70.808, "width_percent": 0.654}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.217653, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 71.462, "width_percent": 0.513}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.2225468, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 71.974, "width_percent": 0.603}, {"sql": "select `score` from `scores` where (`registration_id` = 107 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [107, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.2265449, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 72.577, "width_percent": 0.603}, {"sql": "select * from `registrations` where (`user_id` = 36 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [36, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.235829, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 73.179, "width_percent": 0.558}, {"sql": "select `total` from `total_scores` where (`registration_id` = 107 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [107, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.2404459, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 73.737, "width_percent": 0.577}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.262179, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 74.314, "width_percent": 0.615}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.266336, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 74.929, "width_percent": 0.596}, {"sql": "select `score` from `scores` where (`registration_id` = 103 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [103, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.2702591, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 75.526, "width_percent": 0.737}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.279378, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 76.263, "width_percent": 0.59}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.283555, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 76.853, "width_percent": 0.564}, {"sql": "select `score` from `scores` where (`registration_id` = 103 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [103, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.2877939, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 77.417, "width_percent": 1.25}, {"sql": "select * from `registrations` where (`user_id` = 35 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [35, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.297569, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 78.667, "width_percent": 0.545}, {"sql": "select `total` from `total_scores` where (`registration_id` = 103 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [103, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.302904, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 79.212, "width_percent": 0.583}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.3338509, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 79.795, "width_percent": 0.615}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.3385742, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 80.41, "width_percent": 0.641}, {"sql": "select `score` from `scores` where (`registration_id` = 115 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [115, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.3427489, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 81.051, "width_percent": 0.712}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.350791, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 81.763, "width_percent": 0.558}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.357356, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 82.321, "width_percent": 0.583}, {"sql": "select `score` from `scores` where (`registration_id` = 115 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [115, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.361561, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 82.904, "width_percent": 0.686}, {"sql": "select * from `registrations` where (`user_id` = 38 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [38, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.3678231, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 83.59, "width_percent": 0.526}, {"sql": "select `total` from `total_scores` where (`registration_id` = 115 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [115, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.372024, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 84.115, "width_percent": 0.519}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.393679, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 84.635, "width_percent": 0.506}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.397747, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 85.141, "width_percent": 0.423}, {"sql": "select `score` from `scores` where (`registration_id` = 111 and `course_id` = '326' and `assessment_id` = 1) limit 1", "type": "query", "params": [], "bindings": [111, "326", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.401424, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 85.564, "width_percent": 0.487}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 308}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeDisabled.php", "line": 39}, {"index": 15, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 4}], "start": **********.407952, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:482", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 482}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=482", "ajax": false, "filename": "ScoreResource.php", "line": "482"}, "connection": "racoed", "explain": null, "start_percent": 86.051, "width_percent": 0.455}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 330}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.412473, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 86.506, "width_percent": 0.506}, {"sql": "select `score` from `scores` where (`registration_id` = 111 and `course_id` = '326' and `assessment_id` = 2) limit 1", "type": "query", "params": [], "bindings": [111, "326", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 322}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.4163618, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:340", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=340", "ajax": false, "filename": "ScoreResource.php", "line": "340"}, "connection": "racoed", "explain": null, "start_percent": 87.013, "width_percent": 0.5}, {"sql": "select * from `registrations` where (`user_id` = 37 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1') limit 1", "type": "query", "params": [], "bindings": [37, "3", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 398}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}], "start": **********.425327, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:449", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=449", "ajax": false, "filename": "ScoreResource.php", "line": "449"}, "connection": "racoed", "explain": null, "start_percent": 87.513, "width_percent": 0.673}, {"sql": "select `total` from `total_scores` where (`registration_id` = 111 and `course_id` = '326') limit 1", "type": "query", "params": [], "bindings": [111, "326"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 21, "namespace": "view", "name": "filament-tables::columns.text-input-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-input-column.blade.php", "line": 5}], "start": **********.430012, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:407", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=407", "ajax": false, "filename": "ScoreResource.php", "line": "407"}, "connection": "racoed", "explain": null, "start_percent": 88.186, "width_percent": 0.564}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 1 and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [1, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 15}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.450474, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "filament.hooks.global-portal-access-banner:15", "source": {"index": 14, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=15", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "15"}, "connection": "racoed", "explain": null, "start_percent": 88.75, "width_percent": 0.75}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.4762518, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 89.5, "width_percent": 0.609}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.479837, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 90.109, "width_percent": 0.385}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.483022, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 90.494, "width_percent": 0.455}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.488309, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 90.949, "width_percent": 1.244}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.498851, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 92.192, "width_percent": 0.429}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.502372, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 92.622, "width_percent": 0.346}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.50621, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 92.968, "width_percent": 0.346}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.509764, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 93.314, "width_percent": 0.885}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.519299, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 94.199, "width_percent": 0.442}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.523157, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 94.641, "width_percent": 0.353}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.526633, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 94.994, "width_percent": 0.346}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.5309808, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 95.34, "width_percent": 1.506}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 226}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.540404, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 96.846, "width_percent": 0.417}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 230}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.544135, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:470", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 470}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=470", "ajax": false, "filename": "ScoreResource.php", "line": "470"}, "connection": "racoed", "explain": null, "start_percent": 97.263, "width_percent": 0.41}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.548319, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "ScoreResource.php:237", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource.php", "line": 237}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource.php&line=237", "ajax": false, "filename": "ScoreResource.php", "line": "237"}, "connection": "racoed", "explain": null, "start_percent": 97.673, "width_percent": 0.654}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "1", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 106}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 49}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.5535522, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "ManageScores.php:111", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Clusters/Scores/Resources/ScoreResource/Pages/ManageScores.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FClusters%2FScores%2FResources%2FScoreResource%2FPages%2FManageScores.php&line=111", "ajax": false, "filename": "ManageScores.php", "line": "111"}, "connection": "racoed", "explain": null, "start_percent": 98.327, "width_percent": 1.673}]}, "models": {"data": {"App\\Models\\Department": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\User": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Registration": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\Course": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Grade": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\Score": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FScore.php&line=1", "ajax": false, "filename": "Score.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Semester": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\Assessment": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FAssessment.php&line=1", "ajax": false, "filename": "Assessment.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}}, "count": 94, "is_counter": true}, "livewire": {"data": {"app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores #m03mbYw4LlYpipMvigDX": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"score_filter\" => array:5 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n        \"course_id\" => \"326\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => []\n    \"defaultActionArguments\" => []\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"score_filter\" => array:5 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n        \"course_id\" => \"326\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores\"\n  \"component\" => \"App\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores\"\n  \"id\" => \"m03mbYw4LlYpipMvigDX\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Clusters\\Scores\\Resources\\ScoreResource\\Pages\\ManageScores@updateTableColumnState<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasColumns.php&line=40\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasColumns.php&line=40\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasColumns.php:40-69</a>", "middleware": "web", "duration": "1.26s", "peak_memory": "14MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1543968881 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1543968881\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1805651865 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kC06G50dbGZ2IV1FAwiSeaQX9tJD12xxEVKNd5kn</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1952 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;score_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;,&quot;course_id&quot;:&quot;326&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:true,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;score_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;,&quot;course_id&quot;:&quot;326&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;m03mbYw4LlYpipMvigDX&quot;,&quot;name&quot;:&quot;app.filament.staff.clusters.scores.resources.score-resource.pages.manage-scores&quot;,&quot;path&quot;:&quot;staff\\/scores\\/scores&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;024d602f7d05eb8f602e7465ea7277d186021345ace7fe82faa41f2c785ba793&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"22 characters\">updateTableColumnState</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Scores.2</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">38</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805651865\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-371459123 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkJqd2dSSjRkazZQbTF3NjJISW9aTHc9PSIsInZhbHVlIjoiZkJJS1MzeTRxWWxxMFVDbDNPQTZUSkIrZUxVYmZHR0F0NnNKYWlkQ0V6d2ZOaW9QRmo4eHp2UVJoY21KdVJGRVd3c2JaTW1vZHUvMWxyZUFqNmsvWFNZYU04d0ZKWEg2aHZZQytxNFlmV0xRMldjODVtVjl6bjRNSnpPMzlmaWYxT08zMHNIM1U4NGNvSkcrTmJYcGJKOXhxOTNDUXRDM0FGQkgwNGJHYmRmb2tuKzJYdnBoUEVaOEU2SG9UdG9ycE5xc2RSdFdKTDMrWUQrNGM5em1BQkdiT3BCY2xqeDVMVldGd2xQemhYUT0iLCJtYWMiOiI0NzM3Y2NiM2RhZGE2YTQ2MmJhNjAyY2UyZmNhZmRjOGZiZTRhMzg4ZjJkN2M2ODQzZWM4NjdkOTIyNzgwODQyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBEdjJOUEhvY1pBY2s4djlmNm5HblE9PSIsInZhbHVlIjoiM1BrMW83MnNVQ3Y2NTdKd09kVDJKSjVTMjFpR2wzYVlaeGFxUXU4cTJSczhrS1UvbHppUU50T2lTMEVrQ0ZJN09QM1J5NW5iR1p5YUU1R3hJNTJLcXlBclZydWVWNWNiM3gvaGZpb0JtMlZPZHhYQ3BmSnRhRVVad1hjbEt6VisiLCJtYWMiOiJiNzkxZGI5NWQyMzM0ZGM5MTIyZGRjOGEzZDIwNGU0NjkzMDJiMjg2NzdiNzQ1OTdlZTQ3MGU1NjgwMzljNTYwIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IlFmRWZUbUN5TWlSVTNWWmNpYnppcEE9PSIsInZhbHVlIjoiQ2tGVTluYkQzdTVMTWJZSGo2QmdjUG95VGp4U1BkSlBnUWNKcUJxUkFiUE91TDBwd3NtdGh4c1FOZVFjM0gwbzNsRWR3eVB6d1FBVEgrYjQvMzFGMDBMSHc1dkZEK2Nlb0ZBSEk0KzhzdEN5WEpLQ2ttek9ka1ZybjVPL0NwbzkiLCJtYWMiOiJmOGYzZjAzOTUxYzAwNGI4NjBjNjM2ODM4M2E1ZjNhMjc0ZTJlYTI2MjlmYzU0YjYyYTRmNDU3YTczMzhhY2ZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"262 characters\">https://portal.racoed.test/staff/scores/scores?tableFilters[score_filter][school_session_id]=3&amp;tableFilters[score_filter][semester_id]=1&amp;tableFilters[score_filter][level_id]=1&amp;tableFilters[score_filter][department_id]=16&amp;tableFilters[score_filter][course_id]=326</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2395</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371459123\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-674249808 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|hwcJ4OuuwQL0PF8lFxcDDwdyvAE0tEze6pcPYby2S5UPWNUUuLsx0ygIxRDO|$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kC06G50dbGZ2IV1FAwiSeaQX9tJD12xxEVKNd5kn</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tNqsA2mAWGTQPjNcsrbHj7I5nIy2ikyq9DNmXps1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674249808\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1240996637 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 14 Sep 2025 19:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240996637\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-619676904 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kC06G50dbGZ2IV1FAwiSeaQX9tJD12xxEVKNd5kn</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>ManageScores_filters</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>score_filter</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>school_session_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n        \"<span class=sf-dump-key>course_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">326</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"142 characters\">https://portal.racoed.test/scoresheet/print/scoreData_68c70fa520b93?signature=1e2a2f2782fe3b85b665f250e8cb8d179e33e6678ae9ebf155b23f7c8faef99a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619676904\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}